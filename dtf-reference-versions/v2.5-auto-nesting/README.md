# DTF Gang Builder

A comprehensive web-based tool for creating DTF (Direct-to-Film) gang sheets for custom apparel printing.

## Project Overview

The DTF Gang Builder allows users to:
- Upload images in multiple formats (PNG, JPG, JPEG, GIF, SVG, WEBP)
- Arrange images on customizable gang sheets
- Generate print-ready DTF files
- Save and share projects
- Process orders with email confirmation

## Available Sheet Sizes

- 30x12 inches
- 30x24 inches  
- 30x36 inches
- 30x48 inches
- 30x60 inches
- 30x72 inches
- 30x100 inches
- 30x120 inches

## Technical Specifications

- **Maximum file size**: 100MB
- **Print DPI**: 300 DPI for high-quality output
- **Aspect ratio**: Preservation enabled
- **Output format**: Print-ready PDF

## Key Features

### Upload System
- Drag-and-drop file upload
- Multiple file selection
- Real-time upload progress
- File validation and error handling

### Design Interface
- Interactive canvas with zoom/pan
- Automatic image tiling
- Manual image positioning
- Grid overlay for alignment
- Real-time preview

### Advanced Tools
- Image filters and effects
- Text addition capabilities
- Graphics library integration
- Pre-made design templates
- Autofill optimization

### Project Management
- Save/load functionality
- Shareable project links
- Project versioning
- Collaboration features

### Order Processing
- Design summary and review
- Customer information collection
- Email confirmation system
- Print-ready file generation

## Development Phases

### Phase 1: Core Functionality
- Basic file upload system
- Simple design canvas
- Sheet size configuration
- Basic PDF generation

### Phase 2: Enhanced Features
- Advanced design tools
- Project save/load
- User interface improvements
- Performance optimization

### Phase 3: Order System
- Order confirmation workflow
- Payment integration
- Email notifications
- Customer management

### Phase 4: Advanced Features
- Collaboration tools
- Advanced filters
- Batch processing
- Analytics dashboard

### Phase 5: Production Launch
- Testing and QA
- Performance optimization
- Security hardening
- Deployment and monitoring

## Getting Started

1. Review the comprehensive todo list in `DTF-GANG-BUILDER-TODO.md`
2. Set up the development environment
3. Create the basic folder structure
4. Begin with Phase 1 implementation

## Folder Structure

```
dtf-gang-builder/
├── assets/
│   ├── css/
│   ├── js/
│   ├── images/
│   └── uploads/
├── includes/
│   ├── config.php
│   ├── database.php
│   └── functions.php
├── api/
│   ├── upload.php
│   ├── save-project.php
│   └── generate-pdf.php
├── templates/
│   ├── header.php
│   ├── footer.php
│   └── canvas.php
├── output/
│   └── generated-files/
└── docs/
    ├── api-documentation.md
    └── user-guide.md
```

## Success Metrics

- Upload success rate > 99%
- Design completion rate > 85%
- Order conversion rate > 70%
- User satisfaction score > 4.5/5
- System uptime > 99.9%

## Contributing

Please refer to the detailed todo list for specific implementation tasks and requirements.
