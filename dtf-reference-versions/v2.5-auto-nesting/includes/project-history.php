<?php
/**
 * <PERSON><PERSON> Gang Builder - Project History Management
 * 
 * This file handles project revision tracking, undo/redo functionality, and version management.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * Project History Manager Class
 */
class DTF_ProjectHistory {
    private $db;
    private $max_revisions = 50; // Maximum revisions to keep per project
    
    public function __construct() {
        $this->db = DTF_Database::getInstance();
    }
    
    /**
     * Create project revisions table if not exists
     */
    public function createRevisionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "project_revisions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            revision_number INT NOT NULL,
            canvas_data JSON NOT NULL,
            images_data JSON NULL,
            sheet_size VARCHAR(20) NOT NULL,
            change_description TEXT NULL,
            change_type ENUM('manual', 'auto_save', 'auto_arrange', 'upload', 'delete', 'resize', 'move') DEFAULT 'manual',
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE,
            INDEX idx_project_revisions (project_id, revision_number),
            INDEX idx_created_at (created_at),
            UNIQUE KEY unique_project_revision (project_id, revision_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
    
    /**
     * Save a new revision
     */
    public function saveRevision($project_id, $canvas_data, $images_data = null, $sheet_size = null, $change_description = '', $change_type = 'manual') {
        try {
            // Get user
            $user = dtf_get_or_create_user();
            if (!$user) {
                throw new Exception('User session not found');
            }
            
            // Get project info
            $project = $this->db->fetch(
                "SELECT * FROM " . DTF_DB_PREFIX . "projects WHERE id = :id AND user_id = :user_id",
                ['id' => $project_id, 'user_id' => $user['id']]
            );
            
            if (!$project) {
                throw new Exception('Project not found or access denied');
            }
            
            // Get next revision number
            $latest_revision = $this->db->fetch(
                "SELECT MAX(revision_number) as max_revision FROM " . DTF_DB_PREFIX . "project_revisions WHERE project_id = :project_id",
                ['project_id' => $project_id]
            );
            
            $revision_number = ($latest_revision['max_revision'] ?? 0) + 1;
            
            // Use project sheet size if not provided
            if (!$sheet_size) {
                $sheet_size = $project['sheet_size'];
            }
            
            // Save revision
            $revision_data = [
                'project_id' => $project_id,
                'revision_number' => $revision_number,
                'canvas_data' => is_string($canvas_data) ? $canvas_data : json_encode($canvas_data),
                'images_data' => $images_data ? (is_string($images_data) ? $images_data : json_encode($images_data)) : null,
                'sheet_size' => $sheet_size,
                'change_description' => $change_description,
                'change_type' => $change_type,
                'user_id' => $user['id']
            ];
            
            $revision_id = $this->db->insert('project_revisions', $revision_data);
            
            // Clean up old revisions if we exceed the limit
            $this->cleanupOldRevisions($project_id);
            
            // Update project's current revision
            $this->db->update('projects', [
                'configuration' => $revision_data['canvas_data'],
                'sheet_size' => $sheet_size
            ], 'id = :id', ['id' => $project_id]);
            
            dtf_log('INFO', 'Project revision saved', [
                'project_id' => $project_id,
                'revision_id' => $revision_id,
                'revision_number' => $revision_number,
                'change_type' => $change_type,
                'user_id' => $user['id']
            ]);
            
            return [
                'revision_id' => $revision_id,
                'revision_number' => $revision_number,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Failed to save revision', [
                'project_id' => $project_id,
                'error' => $e->getMessage(),
                'change_type' => $change_type
            ]);
            throw $e;
        }
    }
    
    /**
     * Get project revision history
     */
    public function getRevisionHistory($project_id, $limit = 20, $offset = 0) {
        try {
            $user = dtf_get_or_create_user();
            if (!$user) {
                throw new Exception('User session not found');
            }
            
            // Verify project access
            $project = $this->db->fetch(
                "SELECT id FROM " . DTF_DB_PREFIX . "projects WHERE id = :id AND user_id = :user_id",
                ['id' => $project_id, 'user_id' => $user['id']]
            );
            
            if (!$project) {
                throw new Exception('Project not found or access denied');
            }
            
            // Get revisions
            $revisions = $this->db->fetchAll(
                "SELECT id, revision_number, change_description, change_type, created_at, sheet_size
                 FROM " . DTF_DB_PREFIX . "project_revisions 
                 WHERE project_id = :project_id 
                 ORDER BY revision_number DESC 
                 LIMIT :limit OFFSET :offset",
                [
                    'project_id' => $project_id,
                    'limit' => $limit,
                    'offset' => $offset
                ]
            );
            
            // Get total count
            $total = $this->db->fetch(
                "SELECT COUNT(*) as count FROM " . DTF_DB_PREFIX . "project_revisions WHERE project_id = :project_id",
                ['project_id' => $project_id]
            )['count'];
            
            return [
                'revisions' => $revisions,
                'total' => $total,
                'has_more' => ($offset + $limit) < $total
            ];
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Failed to get revision history', [
                'project_id' => $project_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get specific revision data
     */
    public function getRevision($project_id, $revision_number) {
        try {
            $user = dtf_get_or_create_user();
            if (!$user) {
                throw new Exception('User session not found');
            }
            
            // Verify project access
            $project = $this->db->fetch(
                "SELECT id FROM " . DTF_DB_PREFIX . "projects WHERE id = :id AND user_id = :user_id",
                ['id' => $project_id, 'user_id' => $user['id']]
            );
            
            if (!$project) {
                throw new Exception('Project not found or access denied');
            }
            
            // Get revision
            $revision = $this->db->fetch(
                "SELECT * FROM " . DTF_DB_PREFIX . "project_revisions 
                 WHERE project_id = :project_id AND revision_number = :revision_number",
                [
                    'project_id' => $project_id,
                    'revision_number' => $revision_number
                ]
            );
            
            if (!$revision) {
                throw new Exception('Revision not found');
            }
            
            return $revision;
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Failed to get revision', [
                'project_id' => $project_id,
                'revision_number' => $revision_number,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Restore project to specific revision
     */
    public function restoreRevision($project_id, $revision_number) {
        try {
            $user = dtf_get_or_create_user();
            if (!$user) {
                throw new Exception('User session not found');
            }
            
            // Get the revision to restore
            $revision = $this->getRevision($project_id, $revision_number);
            
            // Create a new revision with the restored data
            $restore_result = $this->saveRevision(
                $project_id,
                $revision['canvas_data'],
                $revision['images_data'],
                $revision['sheet_size'],
                "Restored to revision #{$revision_number}",
                'manual'
            );
            
            dtf_log('INFO', 'Project revision restored', [
                'project_id' => $project_id,
                'restored_from_revision' => $revision_number,
                'new_revision' => $restore_result['revision_number'],
                'user_id' => $user['id']
            ]);
            
            return [
                'restored_revision' => $revision,
                'new_revision' => $restore_result
            ];
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Failed to restore revision', [
                'project_id' => $project_id,
                'revision_number' => $revision_number,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Compare two revisions
     */
    public function compareRevisions($project_id, $revision1, $revision2) {
        try {
            $rev1 = $this->getRevision($project_id, $revision1);
            $rev2 = $this->getRevision($project_id, $revision2);
            
            $canvas1 = json_decode($rev1['canvas_data'], true);
            $canvas2 = json_decode($rev2['canvas_data'], true);
            
            $comparison = [
                'revision1' => [
                    'number' => $rev1['revision_number'],
                    'created_at' => $rev1['created_at'],
                    'change_description' => $rev1['change_description'],
                    'sheet_size' => $rev1['sheet_size']
                ],
                'revision2' => [
                    'number' => $rev2['revision_number'],
                    'created_at' => $rev2['created_at'],
                    'change_description' => $rev2['change_description'],
                    'sheet_size' => $rev2['sheet_size']
                ],
                'differences' => [
                    'sheet_size_changed' => $rev1['sheet_size'] !== $rev2['sheet_size'],
                    'object_count_changed' => count($canvas1['objects'] ?? []) !== count($canvas2['objects'] ?? []),
                    'objects_added' => [],
                    'objects_removed' => [],
                    'objects_modified' => []
                ]
            ];
            
            // Detailed object comparison would go here
            // This is a simplified version
            
            return $comparison;
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Failed to compare revisions', [
                'project_id' => $project_id,
                'revision1' => $revision1,
                'revision2' => $revision2,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Clean up old revisions
     */
    private function cleanupOldRevisions($project_id) {
        try {
            // Keep only the latest N revisions
            $this->db->query(
                "DELETE FROM " . DTF_DB_PREFIX . "project_revisions 
                 WHERE project_id = :project_id 
                 AND revision_number < (
                     SELECT revision_number FROM (
                         SELECT revision_number 
                         FROM " . DTF_DB_PREFIX . "project_revisions 
                         WHERE project_id = :project_id2
                         ORDER BY revision_number DESC 
                         LIMIT 1 OFFSET :max_revisions
                     ) as temp
                 )",
                [
                    'project_id' => $project_id,
                    'project_id2' => $project_id,
                    'max_revisions' => $this->max_revisions
                ]
            );
            
        } catch (Exception $e) {
            // Log but don't throw - cleanup failure shouldn't break the main operation
            dtf_log('WARNING', 'Failed to cleanup old revisions', [
                'project_id' => $project_id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get current revision number for project
     */
    public function getCurrentRevisionNumber($project_id) {
        $latest = $this->db->fetch(
            "SELECT MAX(revision_number) as max_revision FROM " . DTF_DB_PREFIX . "project_revisions WHERE project_id = :project_id",
            ['project_id' => $project_id]
        );
        
        return $latest['max_revision'] ?? 0;
    }
}

// Initialize project history table
$project_history = new DTF_ProjectHistory();
$project_history->createRevisionsTable();

/**
 * Helper functions
 */

/**
 * Get project history instance
 */
function dtf_project_history() {
    return new DTF_ProjectHistory();
}

/**
 * Save project revision
 */
function dtf_save_revision($project_id, $canvas_data, $images_data = null, $sheet_size = null, $description = '', $type = 'manual') {
    $history = dtf_project_history();
    return $history->saveRevision($project_id, $canvas_data, $images_data, $sheet_size, $description, $type);
}

/**
 * Get project revision history
 */
function dtf_get_revision_history($project_id, $limit = 20, $offset = 0) {
    $history = dtf_project_history();
    return $history->getRevisionHistory($project_id, $limit, $offset);
}

/**
 * Restore project revision
 */
function dtf_restore_revision($project_id, $revision_number) {
    $history = dtf_project_history();
    return $history->restoreRevision($project_id, $revision_number);
}

/**
 * Compare project revisions
 */
function dtf_compare_revisions($project_id, $revision1, $revision2) {
    $history = dtf_project_history();
    return $history->compareRevisions($project_id, $revision1, $revision2);
}

?>
