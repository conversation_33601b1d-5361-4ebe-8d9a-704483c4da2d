/**
 * DTF Gang Builder - Main Stylesheet
 * 
 * This file contains all the CSS styles for the DTF Gang Builder application.
 */

/* =============================================================================
   RESET AND BASE STYLES
   ============================================================================= */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* =============================================================================
   HEADER STYLES
   ============================================================================= */

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    font-weight: 300;
    text-align: center;
}

.header .subtitle {
    text-align: center;
    opacity: 0.9;
    margin-top: 0.5rem;
}

/* =============================================================================
   MAIN LAYOUT
   ============================================================================= */

.main-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    padding: 20px 0;
    min-height: calc(100vh - 120px);
}

.sidebar {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.main-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    min-height: 600px;
}

/* =============================================================================
   UPLOAD PANEL STYLES
   ============================================================================= */

.upload-panel {
    margin-bottom: 30px;
}

.upload-panel h3 {
    margin-bottom: 15px;
    color: #333;
    font-weight: 500;
}

.upload-zone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-zone.dragover {
    border-style: solid;
    background: #e8f2ff;
}

.upload-icon {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 15px;
}

.upload-zone:hover .upload-icon,
.upload-zone.dragover .upload-icon {
    color: #667eea;
}

.upload-text {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 10px;
}

.upload-subtext {
    font-size: 0.9rem;
    color: #999;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 15px;
    transition: background 0.3s ease;
}

.upload-button:hover {
    background: #5a6fd8;
}

/* =============================================================================
   PROGRESS STYLES
   ============================================================================= */

.upload-progress {
    margin-top: 20px;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 10px;
    text-align: center;
    font-size: 0.9rem;
    color: #666;
}

/* =============================================================================
   FILE LIST STYLES
   ============================================================================= */

.file-list {
    margin-top: 20px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
}

.file-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 15px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.file-size {
    font-size: 0.8rem;
    color: #666;
}

.file-actions {
    display: flex;
    gap: 10px;
}

.btn-remove {
    background: #ff4757;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
}

.btn-remove:hover {
    background: #ff3742;
}

/* =============================================================================
   SHEET SIZE CONTROLS
   ============================================================================= */

.sheet-controls {
    margin-bottom: 30px;
}

.sheet-controls h3 {
    margin-bottom: 15px;
    color: #333;
    font-weight: 500;
}

.size-selector {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    background: white;
}

.size-info {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #666;
}

/* =============================================================================
   CANVAS AREA STYLES
   ============================================================================= */

.canvas-container {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    min-height: 500px;
}

.canvas-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.canvas-tools {
    display: flex;
    gap: 10px;
}

.tool-button {
    background: white;
    border: 1px solid #ddd;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.tool-button:hover {
    background: #f0f0f0;
}

.tool-button.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.zoom-level {
    font-size: 0.9rem;
    color: #666;
    min-width: 50px;
    text-align: center;
}

#design-canvas {
    display: block;
    margin: 0 auto;
    background: white;
}

/* =============================================================================
   GRID OVERLAY
   ============================================================================= */

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    opacity: 0.3;
}

/* =============================================================================
   ACTION BUTTONS
   ============================================================================= */

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

/* =============================================================================
   MODAL STYLES
   ============================================================================= */

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 500;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.modal-close:hover {
    color: #333;
}

/* =============================================================================
   FORM STYLES
   ============================================================================= */

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
    .main-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px 0;
    }
    
    .sidebar {
        position: static;
        order: 2;
    }
    
    .main-content {
        order: 1;
    }
    
    .canvas-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .canvas-tools {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* =============================================================================
   PROJECT HISTORY STYLES
   ============================================================================= */

.tool-group {
    margin-bottom: 15px;
}

.tool-group .tool-button {
    width: 100%;
    margin-bottom: 5px;
}

/* Professional Controls */
.tool-label {
    display: block;
    font-size: 0.85rem;
    font-weight: 500;
    color: #555;
    margin-bottom: 5px;
}

.tool-input, .tool-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.tool-input:focus, .tool-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    color: #555;
    margin-bottom: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

.checkbox-label:hover {
    color: #333;
}

.history-content {
    max-height: 500px;
    overflow-y: auto;
}

.history-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 15px;
}

.history-info {
    font-size: 0.9rem;
    color: #666;
}

.revision-list {
    max-height: 400px;
    overflow-y: auto;
}

.revision-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
    transition: all 0.3s ease;
}

.revision-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.revision-item.current {
    background: #e8f2ff;
    border-color: #667eea;
}

.revision-info {
    flex: 1;
}

.revision-number {
    font-weight: bold;
    color: #667eea;
    font-size: 1.1rem;
}

.revision-description {
    margin: 5px 0;
    color: #333;
}

.revision-meta {
    font-size: 0.8rem;
    color: #666;
}

.revision-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    text-transform: uppercase;
    font-weight: bold;
    margin-left: 5px;
}

.revision-type.manual {
    background: #28a745;
    color: white;
}

.revision-type.auto_save {
    background: #6c757d;
    color: white;
}

.revision-type.auto_arrange {
    background: #667eea;
    color: white;
}

.revision-type.upload {
    background: #17a2b8;
    color: white;
}

.revision-actions {
    display: flex;
    gap: 5px;
}

.btn-restore {
    background: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
}

.btn-restore:hover {
    background: #218838;
}

.btn-restore:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.loading-message {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* =============================================================================
   UTILITY CLASSES
   ============================================================================= */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.hidden { display: none; }
.visible { display: block; }

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* =============================================================================
   ANIMATIONS
   ============================================================================= */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-up {
    animation: slideUp 0.3s ease;
}
