<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Load Project Test</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 DTF Load Project Functionality Test</h1>
        <p>This page tests the enhanced load project functionality with improved validation and error handling.</p>

        <div class="test-section">
            <h3>1. Test Sample Project Loading</h3>
            <button onclick="testSampleProject()" class="btn">📂 Load Sample Project</button>
            <button onclick="testMinimalProject()" class="btn">📄 Load Minimal Project</button>
            <div id="sampleResult" class="result hidden"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Invalid Project Data</h3>
            <button onclick="testInvalidJSON()" class="btn">❌ Test Invalid JSON</button>
            <button onclick="testMissingFields()" class="btn">⚠️ Test Missing Fields</button>
            <div id="invalidResult" class="result hidden"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Manual Project Input</h3>
            <textarea id="manualProjectInput" rows="10" cols="80" placeholder="Paste project JSON here..."></textarea>
            <br>
            <button onclick="testManualProject()" class="btn btn-success">🔍 Validate & Preview</button>
            <div id="manualResult" class="result hidden"></div>
        </div>

        <div class="test-section">
            <h3>4. Load Project Modal Test</h3>
            <button onclick="openLoadModal()" class="btn">🖼️ Open Load Project Modal</button>
            <div id="modalResult" class="result hidden"></div>
        </div>

        <div id="status" class="result hidden"></div>
    </div>

    <!-- Load Project Modal -->
    <div id="load-project-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📂 Load Project</h3>
                <button class="modal-close" onclick="closeLoadModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="control-group">
                    <label class="control-label">Select Project File</label>
                    <input type="file" id="project-file-input" class="control-input" accept=".dtf,.json">
                </div>

                <div class="or-divider">
                    <span>OR</span>
                </div>

                <div class="control-group">
                    <label class="control-label">Paste Project Data</label>
                    <textarea id="project-data-input" class="control-input" rows="6" placeholder="Paste project JSON data here..."></textarea>
                </div>

                <div id="load-project-preview" class="project-preview hidden">
                    <h4>Project Preview:</h4>
                    <div class="preview-stats">
                        <div class="stat-row">
                            <span>Name:</span>
                            <span id="preview-name">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Description:</span>
                            <span id="preview-description">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Version:</span>
                            <span id="preview-version">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Sheet Size:</span>
                            <span id="preview-sheet-size">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Images:</span>
                            <span id="preview-images">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Image Status:</span>
                            <span id="preview-image-status" class="status-neutral">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Layout Copies:</span>
                            <span id="preview-copies">-</span>
                        </div>
                        <div class="stat-row">
                            <span>Created:</span>
                            <span id="preview-created">-</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeLoadModal()">Cancel</button>
                <button class="btn btn-primary" onclick="loadProject()" id="load-project-confirm-btn" disabled>📂 Load Project</button>
            </div>
        </div>
    </div>

    <!-- Include the DTF Builder Project JavaScript -->
    <script src="assets/js/dtf-builder-project.js"></script>
    
    <script>
        // Mock DTF Builder for testing
        class MockDTFBuilder {
            constructor() {
                this.images = [];
                this.imagePositions = [];
                this.selectedObject = null;
                this.sheetSize = '30x72';
                this.dpi = 300;
                this.spacing = 0.125;
                this.bleed = 0.0625;
            }

            showStatus(message, type) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = `result status ${type}`;
                statusDiv.textContent = message;
                statusDiv.classList.remove('hidden');
                console.log(`[${type.toUpperCase()}] ${message}`);
            }

            updateCanvas() { console.log('Canvas updated'); }
            updateStats() { console.log('Stats updated'); }
            updateImageList() { console.log('Image list updated'); }
            updateButtonStates() { console.log('Button states updated'); }
            drawWithImages() { console.log('Drawing with images'); }
        }

        // Initialize mock builder
        window.dtfBuilder = new MockDTFBuilder();
        
        // Extend with project functionality
        Object.assign(window.dtfBuilder, DTFProjectManager);

        // Test functions
        async function testSampleProject() {
            try {
                const response = await fetch('data/sample-project.dtf');
                const projectData = await response.text();
                
                const validation = window.dtfBuilder.validateProjectData(projectData);
                const resultDiv = document.getElementById('sampleResult');
                resultDiv.classList.remove('hidden');
                
                if (validation.valid) {
                    resultDiv.innerHTML = `
                        <strong>✅ Sample Project Validation: PASSED</strong><br>
                        Project: ${validation.data.name}<br>
                        Images: ${validation.data.images.length}<br>
                        Layout: ${validation.data.layout.length} copies<br>
                        Sheet Size: ${validation.data.settings.sheetSize}
                    `;
                    window.dtfBuilder.previewProjectData(projectData);
                } else {
                    resultDiv.innerHTML = `<strong>❌ Sample Project Validation: FAILED</strong><br>Error: ${validation.error}`;
                }
            } catch (error) {
                document.getElementById('sampleResult').innerHTML = `<strong>❌ Error loading sample project:</strong> ${error.message}`;
                document.getElementById('sampleResult').classList.remove('hidden');
            }
        }

        async function testMinimalProject() {
            try {
                const response = await fetch('data/minimal-project.dtf');
                const projectData = await response.text();
                
                const validation = window.dtfBuilder.validateProjectData(projectData);
                const resultDiv = document.getElementById('sampleResult');
                resultDiv.classList.remove('hidden');
                
                if (validation.valid) {
                    resultDiv.innerHTML = `
                        <strong>✅ Minimal Project Validation: PASSED</strong><br>
                        Project: ${validation.data.name}<br>
                        Images: ${validation.data.images.length} (auto-filled)<br>
                        Layout: ${validation.data.layout.length} copies (auto-filled)<br>
                        Sheet Size: ${validation.data.settings.sheetSize}
                    `;
                } else {
                    resultDiv.innerHTML = `<strong>❌ Minimal Project Validation: FAILED</strong><br>Error: ${validation.error}`;
                }
            } catch (error) {
                document.getElementById('sampleResult').innerHTML = `<strong>❌ Error loading minimal project:</strong> ${error.message}`;
                document.getElementById('sampleResult').classList.remove('hidden');
            }
        }

        function testInvalidJSON() {
            const invalidData = '{ "name": "Test", invalid json }';
            const validation = window.dtfBuilder.validateProjectData(invalidData);
            const resultDiv = document.getElementById('invalidResult');
            resultDiv.classList.remove('hidden');
            
            if (!validation.valid) {
                resultDiv.innerHTML = `<strong>✅ Invalid JSON Test: PASSED</strong><br>Correctly caught error: ${validation.error}`;
            } else {
                resultDiv.innerHTML = `<strong>❌ Invalid JSON Test: FAILED</strong><br>Should have failed validation`;
            }
        }

        function testMissingFields() {
            const missingFieldsData = '{ "description": "Missing name and settings" }';
            const validation = window.dtfBuilder.validateProjectData(missingFieldsData);
            const resultDiv = document.getElementById('invalidResult');
            resultDiv.classList.remove('hidden');
            
            if (!validation.valid) {
                resultDiv.innerHTML = `<strong>✅ Missing Fields Test: PASSED</strong><br>Correctly caught error: ${validation.error}`;
            } else {
                resultDiv.innerHTML = `<strong>❌ Missing Fields Test: FAILED</strong><br>Should have failed validation`;
            }
        }

        function testManualProject() {
            const manualData = document.getElementById('manualProjectInput').value;
            if (!manualData.trim()) {
                alert('Please paste some project JSON data first');
                return;
            }
            
            const validation = window.dtfBuilder.validateProjectData(manualData);
            const resultDiv = document.getElementById('manualResult');
            resultDiv.classList.remove('hidden');
            
            if (validation.valid) {
                resultDiv.innerHTML = `
                    <strong>✅ Manual Project Validation: PASSED</strong><br>
                    Project: ${validation.data.name}<br>
                    Description: ${validation.data.description || 'None'}<br>
                    Images: ${validation.data.images.length}<br>
                    Layout: ${validation.data.layout.length} copies<br>
                    Version: ${validation.data.version}
                `;
                window.dtfBuilder.previewProjectData(manualData);
            } else {
                resultDiv.innerHTML = `<strong>❌ Manual Project Validation: FAILED</strong><br>Error: ${validation.error}`;
            }
        }

        function openLoadModal() {
            window.dtfBuilder.loadProject();
            document.getElementById('modalResult').innerHTML = '<strong>✅ Load modal opened successfully!</strong>';
            document.getElementById('modalResult').classList.remove('hidden');
        }

        function closeLoadModal() {
            document.getElementById('load-project-modal').classList.add('hidden');
        }

        function loadProject() {
            window.dtfBuilder.loadProjectData();
        }

        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // File input change handler
            const fileInput = document.getElementById('project-file-input');
            if (fileInput) {
                fileInput.addEventListener('change', function() {
                    if (this.files.length > 0 && window.dtfBuilder) {
                        const file = this.files[0];
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            window.dtfBuilder.previewProjectData(e.target.result);
                        };
                        reader.readAsText(file);
                    }
                });
            }
            
            // Text input change handler
            const textInput = document.getElementById('project-data-input');
            if (textInput) {
                textInput.addEventListener('input', function() {
                    if (this.value.trim() && window.dtfBuilder) {
                        window.dtfBuilder.previewProjectData(this.value);
                    }
                });
            }
        });
    </script>
</body>
</html>
