# DTF Gang Builder - Git Ignore File

# =============================================================================
# ENVIRONMENT & CONFIGURATION
# =============================================================================

# Environment files
.env
.env.local
.env.production
.env.staging
config.local.php

# =============================================================================
# UPLOADS & GENERATED FILES
# =============================================================================

# User uploads
assets/uploads/*
!assets/uploads/.gitkeep

# Generated output files
output/generated-files/*
!output/generated-files/.gitkeep

# Temporary files
temp/*
!temp/.gitkeep

# Cache files
temp/cache/*
!temp/cache/.gitkeep

# Processing files
temp/processing/*
!temp/processing/.gitkeep

# Thumbnails
temp/thumbnails/*
!temp/thumbnails/.gitkeep

# Quarantine
temp/quarantine/*
!temp/quarantine/.gitkeep

# =============================================================================
# LOGS & DEBUGGING
# =============================================================================

# Log files
logs/*
!logs/.gitkeep
*.log

# Debug files
debug.php
test.php
dump.sql

# =============================================================================
# DEPENDENCIES & LIBRARIES
# =============================================================================

# Composer
vendor/
composer.lock

# Node modules (if using npm/yarn)
node_modules/
package-lock.json
yarn.lock

# Downloaded libraries
assets/js/fabric.js
assets/js/tcpdf/
assets/css/bootstrap.min.css

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PhpStorm
.idea/
*.iml

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# =============================================================================
# BACKUP & ARCHIVE FILES
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig
*.save
*.tmp

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Database dumps
*.sql
*.sqlite
*.db

# =============================================================================
# SECURITY & SENSITIVE DATA
# =============================================================================

# Private keys
*.pem
*.key
*.p12
*.pfx

# SSL certificates
*.crt
*.cer

# Configuration with sensitive data
config.production.php
database.production.php

# =============================================================================
# TESTING & DEVELOPMENT
# =============================================================================

# PHPUnit
phpunit.xml
.phpunit.result.cache
coverage/

# Testing files
tests/temp/
tests/uploads/
tests/output/

# Development tools
.php_cs.cache
.php-cs-fixer.cache

# =============================================================================
# DOCUMENTATION BUILD FILES
# =============================================================================

# Generated documentation
docs/build/
docs/html/
docs/latex/

# =============================================================================
# PROJECT SPECIFIC
# =============================================================================

# Custom exclusions for DTF Gang Builder

# Large sample files
samples/large/
samples/*.psd
samples/*.ai

# Customer data (if any)
customer-data/
orders/

# Production configuration
production.config.php

# Local development overrides
local.config.php
dev.config.php

# Performance profiling
*.prof
profiler/

# =============================================================================
# KEEP FILES
# =============================================================================

# Keep empty directories with .gitkeep files
!.gitkeep
