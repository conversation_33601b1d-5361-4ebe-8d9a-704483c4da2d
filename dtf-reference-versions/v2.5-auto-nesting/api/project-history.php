<?php
/**
 * DTF Gang Builder - Project History API
 * 
 * This endpoint handles project revision tracking, undo/redo, and version management.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/project-history.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }
    
    $history = dtf_project_history();
    
    switch ($method) {
        case 'GET':
            handleGetRequest($history, $action);
            break;
            
        case 'POST':
            handlePostRequest($history, $action);
            break;
            
        default:
            dtf_error_response('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Project history API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'action' => $action ?? 'unknown'
    ]);
    
    dtf_error_response('Operation failed: ' . $e->getMessage(), 500);
}

/**
 * Handle GET requests
 */
function handleGetRequest($history, $action) {
    switch ($action) {
        case 'list':
            getRevisionHistory($history);
            break;
            
        case 'get':
            getRevision($history);
            break;
            
        case 'compare':
            compareRevisions($history);
            break;
            
        default:
            dtf_error_response('Invalid action for GET request');
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($history, $action) {
    switch ($action) {
        case 'save':
            saveRevision($history);
            break;
            
        case 'restore':
            restoreRevision($history);
            break;
            
        default:
            dtf_error_response('Invalid action for POST request');
    }
}

/**
 * Get revision history
 */
function getRevisionHistory($history) {
    $project_id = $_GET['project_id'] ?? null;
    $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
    $offset = max(0, intval($_GET['offset'] ?? 0));
    
    if (!$project_id) {
        dtf_error_response('Project ID is required');
    }
    
    $result = $history->getRevisionHistory($project_id, $limit, $offset);
    
    dtf_success_response($result, 'Revision history retrieved successfully');
}

/**
 * Get specific revision
 */
function getRevision($history) {
    $project_id = $_GET['project_id'] ?? null;
    $revision_number = $_GET['revision_number'] ?? null;
    
    if (!$project_id || !$revision_number) {
        dtf_error_response('Project ID and revision number are required');
    }
    
    $revision = $history->getRevision($project_id, $revision_number);
    
    // Parse JSON data for response
    $revision['canvas_data'] = json_decode($revision['canvas_data'], true);
    if ($revision['images_data']) {
        $revision['images_data'] = json_decode($revision['images_data'], true);
    }
    
    dtf_success_response($revision, 'Revision retrieved successfully');
}

/**
 * Compare revisions
 */
function compareRevisions($history) {
    $project_id = $_GET['project_id'] ?? null;
    $revision1 = $_GET['revision1'] ?? null;
    $revision2 = $_GET['revision2'] ?? null;
    
    if (!$project_id || !$revision1 || !$revision2) {
        dtf_error_response('Project ID and both revision numbers are required');
    }
    
    $comparison = $history->compareRevisions($project_id, $revision1, $revision2);
    
    dtf_success_response($comparison, 'Revision comparison completed');
}

/**
 * Save new revision
 */
function saveRevision($history) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    $required_fields = ['project_id', 'canvas_data'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            dtf_error_response("Missing required field: {$field}");
        }
    }
    
    $project_id = $input['project_id'];
    $canvas_data = $input['canvas_data'];
    $images_data = $input['images_data'] ?? null;
    $sheet_size = $input['sheet_size'] ?? null;
    $description = $input['description'] ?? '';
    $change_type = $input['change_type'] ?? 'manual';
    
    $result = $history->saveRevision(
        $project_id,
        $canvas_data,
        $images_data,
        $sheet_size,
        $description,
        $change_type
    );
    
    dtf_success_response($result, 'Revision saved successfully');
}

/**
 * Restore revision
 */
function restoreRevision($history) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        dtf_error_response('Invalid JSON data');
    }
    
    $project_id = $input['project_id'] ?? null;
    $revision_number = $input['revision_number'] ?? null;
    
    if (!$project_id || !$revision_number) {
        dtf_error_response('Project ID and revision number are required');
    }
    
    $result = $history->restoreRevision($project_id, $revision_number);
    
    // Parse JSON data for response
    $result['restored_revision']['canvas_data'] = json_decode($result['restored_revision']['canvas_data'], true);
    if ($result['restored_revision']['images_data']) {
        $result['restored_revision']['images_data'] = json_decode($result['restored_revision']['images_data'], true);
    }
    
    dtf_success_response($result, 'Revision restored successfully');
}
?>
