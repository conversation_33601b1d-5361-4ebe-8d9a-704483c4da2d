<?php
/**
 * DTF Gang Builder - Main Application Entry Point
 * 
 * This is the main entry point for the DTF Gang Builder application.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';

// Get or create user session
$user = dtf_get_or_create_user();

// Log page access
dtf_log('INFO', 'Main page accessed', ['user_id' => $user['id']]);

// Get available sheet sizes
$sheet_sizes = unserialize(DTF_SHEET_SIZES);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo dtf_generate_csrf_token(); ?>">
    <title><?php echo DTF_APP_NAME; ?> - Create Professional DTF Gang Sheets</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Create professional DTF gang sheets with our easy-to-use online tool. Upload images, arrange them on customizable sheets, and generate print-ready files.">
    <meta name="keywords" content="DTF, gang sheet, direct to film, printing, custom apparel, t-shirt printing">
    <meta name="author" content="CYPTSHOP">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1><?php echo DTF_APP_NAME; ?></h1>
            <p class="subtitle">Create Professional DTF Gang Sheets with Ease</p>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <!-- Upload Panel -->
                <div class="upload-panel">
                    <h3><i class="fas fa-upload"></i> Upload Images</h3>
                    
                    <div id="upload-zone" class="upload-zone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            Drag & drop images here
                        </div>
                        <div class="upload-subtext">
                            or click to browse files
                        </div>
                        <input type="file" id="file-input" class="file-input" multiple accept="image/*">
                    </div>
                    
                    <button id="upload-button" class="upload-button">
                        <i class="fas fa-folder-open"></i> Choose Files
                    </button>
                    
                    <!-- Upload Progress -->
                    <div id="upload-progress" class="upload-progress">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <div id="progress-text" class="progress-text">Uploading...</div>
                    </div>
                    
                    <!-- File List -->
                    <div id="file-list" class="file-list"></div>
                </div>

                <!-- Sheet Size Controls -->
                <div class="sheet-controls">
                    <h3><i class="fas fa-ruler-combined"></i> Sheet Size</h3>
                    
                    <select id="sheet-size" class="size-selector">
                        <?php foreach ($sheet_sizes as $size => $dimensions): ?>
                            <option value="<?php echo $size; ?>" <?php echo $size === DTF_DEFAULT_SHEET_SIZE ? 'selected' : ''; ?>>
                                <?php echo $size; ?>" (<?php echo $dimensions['width']; ?>" × <?php echo $dimensions['height']; ?>")
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <div id="size-info" class="size-info">
                        Select a sheet size to see dimensions
                    </div>
                </div>

                <!-- Design Tools -->
                <div class="design-tools">
                    <h3><i class="fas fa-tools"></i> Tools</h3>

                    <div class="tool-group">
                        <button id="auto-arrange" class="tool-button">
                            <i class="fas fa-magic"></i> Auto Arrange
                        </button>
                        <button id="optimize-layout" class="tool-button">
                            <i class="fas fa-compress-arrows-alt"></i> Optimize
                        </button>
                        <button id="toggle-grid" class="tool-button">
                            <i class="fas fa-th"></i> Hide Grid
                        </button>
                    </div>

                    <div class="tool-group">
                        <button id="undo-btn" class="tool-button" disabled title="Undo">
                            <i class="fas fa-undo"></i> Undo
                        </button>
                        <button id="redo-btn" class="tool-button" disabled title="Redo">
                            <i class="fas fa-redo"></i> Redo
                        </button>
                    </div>

                    <div class="tool-group">
                        <button id="duplicate-selected" class="tool-button" disabled>
                            <i class="fas fa-copy"></i> Duplicate
                        </button>
                        <button id="delete-selected" class="tool-button" disabled>
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>

                <!-- Professional Settings -->
                <div class="design-tools">
                    <h3><i class="fas fa-cogs"></i> Professional Settings</h3>

                    <div class="tool-group">
                        <label class="tool-label">Spacing (mm)</label>
                        <input type="number" id="image-spacing" class="tool-input" value="3" min="0" max="20" step="0.5">
                    </div>

                    <div class="tool-group">
                        <label class="tool-label">Bleed Area (mm)</label>
                        <input type="number" id="bleed-area" class="tool-input" value="1" min="0" max="10" step="0.5">
                    </div>

                    <div class="tool-group">
                        <label class="tool-label">Nesting Algorithm</label>
                        <select id="nesting-algorithm" class="tool-select">
                            <option value="efficiency" selected>Max Efficiency</option>
                            <option value="speed">Fastest</option>
                            <option value="uniform">Uniform</option>
                            <option value="rows">Row-by-Row</option>
                        </select>
                    </div>

                    <div class="tool-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-rotate" checked> Auto-rotate images
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="add-crop-marks"> Add crop marks
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="safety-margins" checked> Safety margins
                        </label>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Canvas Container -->
                <div class="canvas-container">
                    <!-- Canvas Toolbar -->
                    <div class="canvas-toolbar">
                        <div class="canvas-tools">
                            <button id="zoom-in" class="tool-button" title="Zoom In">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button id="zoom-out" class="tool-button" title="Zoom Out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button id="zoom-fit" class="tool-button" title="Fit to View">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>
                        
                        <div class="zoom-controls">
                            <span>Zoom:</span>
                            <span id="zoom-level" class="zoom-level">100%</span>
                        </div>
                    </div>

                    <!-- Design Canvas -->
                    <canvas id="design-canvas"></canvas>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button id="save-project" class="btn btn-secondary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                    <button id="generate-pdf" class="btn btn-primary">
                        <i class="fas fa-file-pdf"></i> Generate PDF
                    </button>
                    <button id="clear-canvas" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> Clear Canvas
                    </button>
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php echo DTF_APP_NAME; ?>. All rights reserved.</p>
            <p>Supported formats: PNG, JPG, JPEG, GIF, SVG, WEBP | Max file size: <?php echo dtf_format_bytes(DTF_MAX_FILE_SIZE); ?></p>
        </div>
    </footer>

    <!-- Modals -->
    
    <!-- Save Project Modal -->
    <div id="save-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Save Project</h3>
                <button class="modal-close" onclick="closeSaveModal()">&times;</button>
            </div>
            <form id="save-form">
                <div class="form-group">
                    <label class="form-label" for="project-name">Project Name</label>
                    <input type="text" id="project-name" class="form-input" placeholder="Enter project name" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="project-description">Description (Optional)</label>
                    <textarea id="project-description" class="form-input form-textarea" placeholder="Enter project description"></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Save Project</button>
                    <button type="button" class="btn btn-secondary" onclick="closeSaveModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Order Confirmation Modal -->
    <div id="order-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Order Confirmation</h3>
                <button class="modal-close" onclick="closeOrderModal()">&times;</button>
            </div>
            <form id="order-form">
                <div class="form-group">
                    <label class="form-label" for="customer-email">Email Address</label>
                    <input type="email" id="customer-email" class="form-input" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="customer-name">Full Name</label>
                    <input type="text" id="customer-name" class="form-input" placeholder="Enter your full name" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="quantity">Quantity</label>
                    <input type="number" id="quantity" class="form-input" value="1" min="1" max="100" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="special-instructions">Special Instructions (Optional)</label>
                    <textarea id="special-instructions" class="form-input form-textarea" placeholder="Any special instructions for your order"></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success">Confirm Order</button>
                    <button type="button" class="btn btn-secondary" onclick="closeOrderModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Revision History Modal -->
    <div id="history-modal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">Project History</h3>
                <button class="modal-close" onclick="closeHistoryModal()">&times;</button>
            </div>
            <div class="history-content">
                <div class="history-toolbar">
                    <button id="refresh-history" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                    <span class="history-info">
                        Current Revision: <span id="current-revision-number">-</span>
                    </span>
                </div>
                <div id="revision-list" class="revision-list">
                    <div class="loading-message">Loading revision history...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Revision Modal -->
    <div id="save-revision-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Save Revision</h3>
                <button class="modal-close" onclick="closeSaveRevisionModal()">&times;</button>
            </div>
            <form id="save-revision-form">
                <div class="form-group">
                    <label class="form-label" for="revision-description">Description</label>
                    <input type="text" id="revision-description" class="form-input" placeholder="Describe the changes made" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Save Revision</button>
                    <button type="button" class="btn btn-secondary" onclick="closeSaveRevisionModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    
    <!-- Fabric.js for canvas manipulation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <!-- Main application JavaScript -->
    <script src="assets/js/main.js"></script>

    <!-- Additional JavaScript for modals and UI interactions -->
    <script>
        // Modal functions
        function openSaveModal() {
            document.getElementById('save-modal').classList.add('show');
        }

        function closeSaveModal() {
            document.getElementById('save-modal').classList.remove('show');
        }

        function openOrderModal() {
            document.getElementById('order-modal').classList.add('show');
        }

        function closeOrderModal() {
            document.getElementById('order-modal').classList.remove('show');
        }

        function openHistoryModal() {
            document.getElementById('history-modal').classList.add('show');
            if (window.dtfBuilder) {
                window.dtfBuilder.loadRevisionHistory();
            }
        }

        function closeHistoryModal() {
            document.getElementById('history-modal').classList.remove('show');
        }

        function openSaveRevisionModal() {
            document.getElementById('save-revision-modal').classList.add('show');
        }

        function closeSaveRevisionModal() {
            document.getElementById('save-revision-modal').classList.remove('show');
        }

        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            const saveModal = document.getElementById('save-modal');
            const orderModal = document.getElementById('order-modal');
            const historyModal = document.getElementById('history-modal');
            const saveRevisionModal = document.getElementById('save-revision-modal');

            if (e.target === saveModal) {
                closeSaveModal();
            }
            if (e.target === orderModal) {
                closeOrderModal();
            }
            if (e.target === historyModal) {
                closeHistoryModal();
            }
            if (e.target === saveRevisionModal) {
                closeSaveRevisionModal();
            }
        });

        // Auto-arrange functionality
        document.getElementById('auto-arrange')?.addEventListener('click', function() {
            if (window.dtfBuilder && window.dtfBuilder.uploadedImages.length > 0) {
                window.dtfBuilder.autoArrangeImages();
            } else {
                alert('Please upload some images first');
            }
        });

        // Optimize layout
        document.getElementById('optimize-layout')?.addEventListener('click', function() {
            if (window.dtfBuilder && window.dtfBuilder.uploadedImages.length > 0) {
                window.dtfBuilder.optimizeLayout();
            } else {
                alert('Please upload some images first');
            }
        });

        // Professional settings event handlers
        document.getElementById('image-spacing')?.addEventListener('input', function(e) {
            if (window.dtfBuilder) {
                window.dtfBuilder.imageSpacing = parseFloat(e.target.value) || 3;
            }
        });

        document.getElementById('bleed-area')?.addEventListener('input', function(e) {
            if (window.dtfBuilder) {
                window.dtfBuilder.bleedArea = parseFloat(e.target.value) || 1;
            }
        });

        document.getElementById('nesting-algorithm')?.addEventListener('change', function(e) {
            if (window.dtfBuilder) {
                window.dtfBuilder.nestingAlgorithm = e.target.value;
            }
        });

        document.getElementById('auto-rotate')?.addEventListener('change', function(e) {
            if (window.dtfBuilder) {
                window.dtfBuilder.autoRotate = e.target.checked;
            }
        });

        document.getElementById('add-crop-marks')?.addEventListener('change', function(e) {
            if (window.dtfBuilder) {
                window.dtfBuilder.addCropMarks = e.target.checked;
            }
        });

        document.getElementById('safety-margins')?.addEventListener('change', function(e) {
            if (window.dtfBuilder) {
                window.dtfBuilder.safetyMargins = e.target.checked;
            }
        });

        // Object manipulation
        document.getElementById('duplicate-selected')?.addEventListener('click', function() {
            if (window.dtfBuilder) {
                window.dtfBuilder.duplicateSelected();
            }
        });

        document.getElementById('delete-selected')?.addEventListener('click', function() {
            if (window.dtfBuilder) {
                window.dtfBuilder.deleteSelected();
            }
        });

        // Undo/Redo functionality
        document.getElementById('undo-btn')?.addEventListener('click', function() {
            if (window.dtfBuilder) {
                window.dtfBuilder.undo();
            }
        });

        document.getElementById('redo-btn')?.addEventListener('click', function() {
            if (window.dtfBuilder) {
                window.dtfBuilder.redo();
            }
        });

        // Save revision functionality
        document.getElementById('save-revision-btn')?.addEventListener('click', function() {
            openSaveRevisionModal();
        });

        // History functionality
        document.getElementById('history-btn')?.addEventListener('click', function() {
            openHistoryModal();
        });

        // Refresh history
        document.getElementById('refresh-history')?.addEventListener('click', function() {
            if (window.dtfBuilder) {
                window.dtfBuilder.loadRevisionHistory();
            }
        });

        // Save revision form
        document.getElementById('save-revision-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            const description = document.getElementById('revision-description').value;
            if (window.dtfBuilder && description.trim()) {
                window.dtfBuilder.saveRevision('manual', description.trim()).then(() => {
                    closeSaveRevisionModal();
                    document.getElementById('revision-description').value = '';
                });
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Z for undo
            if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                if (window.dtfBuilder) {
                    window.dtfBuilder.undo();
                }
            }

            // Ctrl+Y or Ctrl+Shift+Z for redo
            if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'Z')) {
                e.preventDefault();
                if (window.dtfBuilder) {
                    window.dtfBuilder.redo();
                }
            }

            // Ctrl+S for save revision
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                openSaveRevisionModal();
            }
        });

        // Application info
        console.log('<?php echo DTF_APP_NAME; ?> v<?php echo DTF_APP_VERSION; ?> loaded');
        console.log('Environment: <?php echo DTF_ENVIRONMENT; ?>');
        console.log('Debug mode: <?php echo DTF_DEBUG ? 'enabled' : 'disabled'; ?>');
    </script>
</body>
</html>
