<?php
/**
 * CYPTSHOP Simple Cart Sidebar
 * Working cart sidebar that matches JavaScript
 */
?>

<!-- Cart Sidebar Overlay -->
<div id="cartSidebarOverlay" class="cart-sidebar-overlay" onclick="closeCartSidebar()"></div>

<!-- Simple Cart Sidebar -->
<div id="cartSidebar" class="cart-sidebar">
    <!-- Car<PERSON> Header -->
    <div class="cart-sidebar-header bg-dark-grey-2 p-3 border-bottom border-cyan">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="text-cyan mb-0">
                <i class="fas fa-shopping-cart me-2"></i>
                Shopping Cart
                <span class="badge bg-magenta text-black ms-2" id="cartCountBadge">0</span>
            </h5>
            <button class="btn btn-sm btn-outline-cyan" onclick="closeCartSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Cart Body -->
    <div class="cart-sidebar-body flex-grow-1 p-3">
        <!-- Empty Cart Message -->
        <div id="emptyCartMessage" class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-dark-grey-3 mb-3"></i>
            <h6 class="text-white mb-2">Your cart is empty</h6>
            <p class="text-off-white mb-3">Add some products to get started!</p>
            <button class="btn btn-cyan" onclick="closeCartSidebar()">
                <i class="fas fa-arrow-left me-1"></i>Continue Shopping
            </button>
        </div>

        <!-- Cart Items Container -->
        <div id="cartItems"></div>
    </div>

    <!-- Cart Footer -->
    <div class="cart-sidebar-footer bg-dark-grey-2 p-3 border-top border-cyan">
        <div class="cart-summary mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <span class="text-white">Total:</span>
                <span class="text-yellow fw-bold h5 mb-0" id="cartTotal">$0.00</span>
            </div>
        </div>
        
        <div class="cart-actions">
            <a href="/cart/" class="btn btn-outline-cyan w-100 mb-2">
                <i class="fas fa-shopping-cart me-2"></i>View Cart
            </a>
            <button class="btn btn-magenta w-100 mb-2" onclick="proceedToCheckout()">
                <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
            </button>
            <button class="btn btn-outline-danger w-100" onclick="clearCart()">
                <i class="fas fa-trash me-2"></i>Clear Cart
            </button>
        </div>
    </div>
</div>

<!-- Cart Sidebar CSS -->
<style>
/* Override existing cart sidebar styles with higher specificity */
#cartSidebarOverlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.7) !important;
    z-index: 9998 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
}

#cartSidebarOverlay.active {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

#cartSidebar {
    position: fixed !important;
    top: 0 !important;
    right: -400px !important;
    width: 400px !important;
    height: 100vh !important;
    background: #1a1a1a !important;
    border-left: 2px solid #00FFFF !important;
    z-index: 9999 !important;
    transition: right 0.3s ease !important;
    display: flex !important;
    flex-direction: column !important;
    box-shadow: -5px 0 20px rgba(0, 255, 255, 0.2) !important;
    transform: none !important; /* Override transform styles */
}

#cartSidebar.active {
    right: 0 !important;
    transform: none !important; /* Override transform styles */
}

@media (max-width: 768px) {
    #cartSidebar {
        width: 100% !important;
        right: -100% !important;
    }

    #cartSidebar.active {
        right: 0 !important;
    }
}
</style>
