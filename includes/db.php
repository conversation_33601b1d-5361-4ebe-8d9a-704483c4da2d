<?php
/**
 * MySQL Database Management Functions
 * CYPTSHOP - Phase 2: Complete MySQL Migration (No JSON Dependencies)
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

// Include database connection
require_once 'database.php';

/**
 * Get data from MySQL table (replaces getJsonData)
 * Note: This function is now defined in includes/database.php to avoid conflicts
 * Removed duplicate declaration - use the one in database.php
 */
// function getTableData() moved to includes/database.php

/**
 * Insert data into MySQL table (replaces saveJsonData for new records)
 * @param string $table Table name
 * @param array $data Data to insert
 * @return int|bool Insert ID on success, false on failure
 */
function insertTableData($table, $data) {
    if (!isDatabaseAvailable()) {
        return saveJsonDataFallback($table, $data, 'insert');
    }

    try {
        $pdo = getDatabaseConnection();

        $columns = array_keys($data);
        $placeholders = str_repeat('?,', count($columns) - 1) . '?';

        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES ({$placeholders})";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute(array_values($data));

        return $result ? $pdo->lastInsertId() : false;

    } catch (PDOException $e) {
        error_log("Database error in insertTableData: " . $e->getMessage());
        return saveJsonDataFallback($table, $data, 'insert');
    }
}

/**
 * Update data in MySQL table (replaces saveJsonData for updates)
 * @param string $table Table name
 * @param array $data Data to update
 * @param array $conditions WHERE conditions
 * @return bool Success status
 */
function updateTableData($table, $data, $conditions) {
    if (!isDatabaseAvailable()) {
        return saveJsonDataFallback($table, $data, 'update', $conditions);
    }

    try {
        $pdo = getDatabaseConnection();

        $setClause = [];
        $params = [];

        foreach ($data as $column => $value) {
            $setClause[] = "`{$column}` = ?";
            $params[] = $value;
        }

        $whereClause = [];
        foreach ($conditions as $column => $value) {
            $whereClause[] = "`{$column}` = ?";
            $params[] = $value;
        }

        $sql = "UPDATE `{$table}` SET " . implode(', ', $setClause) . " WHERE " . implode(' AND ', $whereClause);

        $stmt = $pdo->prepare($sql);
        return $stmt->execute($params);

    } catch (PDOException $e) {
        error_log("Database error in updateTableData: " . $e->getMessage());
        return saveJsonDataFallback($table, $data, 'update', $conditions);
    }
}

/**
 * Delete data from MySQL table
 * @param string $table Table name
 * @param array $conditions WHERE conditions
 * @return bool Success status
 */
function deleteTableData($table, $conditions) {
    if (!isDatabaseAvailable()) {
        return saveJsonDataFallback($table, [], 'delete', $conditions);
    }

    try {
        $pdo = getDatabaseConnection();

        $whereClause = [];
        $params = [];

        foreach ($conditions as $column => $value) {
            $whereClause[] = "`{$column}` = ?";
            $params[] = $value;
        }

        $sql = "DELETE FROM `{$table}` WHERE " . implode(' AND ', $whereClause);

        $stmt = $pdo->prepare($sql);
        return $stmt->execute($params);

    } catch (PDOException $e) {
        error_log("Database error in deleteTableData: " . $e->getMessage());
        return saveJsonDataFallback($table, [], 'delete', $conditions);
    }
}

/**
 * Get single record from MySQL table
 * @param string $table Table name
 * @param array $conditions WHERE conditions
 * @return array|null Single record or null
 */
function getTableRecord($table, $conditions) {
    $results = getTableData($table, $conditions, '', 1);
    return !empty($results) ? $results[0] : null;
}

/**
 * Count records in MySQL table
 * @param string $table Table name
 * @param array $conditions WHERE conditions
 * @return int Record count
 */
function countTableRecords($table, $conditions = []) {
    if (!isDatabaseAvailable()) {
        $data = getJsonDataFallback($table, $conditions);
        return count($data);
    }

    try {
        $pdo = getDatabaseConnection();

        $sql = "SELECT COUNT(*) FROM `{$table}`";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                $whereClause[] = "`{$column}` = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return (int)$stmt->fetchColumn();

    } catch (PDOException $e) {
        error_log("Database error in countTableRecords: " . $e->getMessage());
        $data = getJsonDataFallback($table, $conditions);
        return count($data);
    }
}

/**
 * JSON fallback functions for when database is not available
 */
function getJsonDataFallback($table, $conditions = []) {
    $file = BASE_PATH . "assets/data/{$table}.json";

    if (!file_exists($file)) {
        return [];
    }

    $content = file_get_contents($file);
    $data = json_decode($content, true) ?: [];

    if (empty($conditions)) {
        return $data;
    }

    // Filter by conditions
    return array_filter($data, function($item) use ($conditions) {
        foreach ($conditions as $key => $value) {
            if (!isset($item[$key]) || $item[$key] != $value) {
                return false;
            }
        }
        return true;
    });
}

function saveJsonDataFallback($table, $data, $operation = 'insert', $conditions = []) {
    $file = BASE_PATH . "assets/data/{$table}.json";

    // Get existing data
    $existingData = [];
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $existingData = json_decode($content, true) ?: [];
    }

    switch ($operation) {
        case 'insert':
            $data['id'] = $data['id'] ?? uniqid();
            $existingData[] = $data;
            break;

        case 'update':
            foreach ($existingData as &$item) {
                $match = true;
                foreach ($conditions as $key => $value) {
                    if (!isset($item[$key]) || $item[$key] != $value) {
                        $match = false;
                        break;
                    }
                }
                if ($match) {
                    $item = array_merge($item, $data);
                }
            }
            break;

        case 'delete':
            $existingData = array_filter($existingData, function($item) use ($conditions) {
                foreach ($conditions as $key => $value) {
                    if (isset($item[$key]) && $item[$key] == $value) {
                        return false;
                    }
                }
                return true;
            });
            break;
    }

    // Save back to file
    $jsonData = json_encode(array_values($existingData), JSON_PRETTY_PRINT);
    return file_put_contents($file, $jsonData, LOCK_EX) !== false;
}

/**
 * Legacy function compatibility (deprecated - use MySQL functions)
 */
function getJsonData($file) {
    error_log("DEPRECATED: getJsonData() called. Use getTableData() instead.");
    $table = basename($file, '.json');
    return getTableData($table);
}

function saveJsonData($file, $data) {
    error_log("DEPRECATED: saveJsonData() called. Use insertTableData() or updateTableData() instead.");
    $table = basename($file, '.json');

    if (isset($data['id'])) {
        return updateTableData($table, $data, ['id' => $data['id']]);
    } else {
        return insertTableData($table, $data);
    }
}
?>
