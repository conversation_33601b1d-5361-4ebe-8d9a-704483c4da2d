<?php
/**
 * Common Footer Template
 * CYPTSHOP - Task 3.1.1.2: Footer Component
 */
?>

<!-- Footer -->
<footer class="bg-black text-white mt-5">
    <div class="container py-5">
        <div class="row">
            <!-- Company Info -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h5 class="text-cyan mb-3">
                    <i class="fas fa-tshirt me-2"></i>
                    CYPTSHOP
                </h5>
                <p class="text-off-white">
                    Detroit-style custom design shop specializing in bold T-shirts,
                    apparel design, print services, and digital marketing solutions.
                </p>
                <div class="d-flex gap-3">
                    <a href="#" class="text-cyan fs-4"><i class="fab fa-facebook"></i></a>
                    <a href="#" class="text-magenta fs-4"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-yellow fs-4"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-cyan fs-4"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-magenta mb-3">Quick Links</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="<?php echo SITE_URL; ?>" class="text-off-white text-decoration-none">Home</a></li>
                    <li class="mb-2"><a href="<?php echo SITE_URL; ?>/shop.php" class="text-off-white text-decoration-none">Shop</a></li>
                    <li class="mb-2"><a href="<?php echo SITE_URL; ?>/services.php" class="text-off-white text-decoration-none">Services</a></li>
                    <li class="mb-2"><a href="<?php echo SITE_URL; ?>/portfolio.php" class="text-off-white text-decoration-none">Portfolio</a></li>
                    <li class="mb-2"><a href="<?php echo SITE_URL; ?>/contact.php" class="text-off-white text-decoration-none">Contact</a></li>
                </ul>
            </div>

            <!-- Services -->
            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="text-yellow mb-3">Our Services</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><span class="text-off-white">Custom T-Shirts</span></li>
                    <li class="mb-2"><span class="text-off-white">Apparel Design</span></li>
                    <li class="mb-2"><span class="text-off-white">Print Services</span></li>
                    <li class="mb-2"><span class="text-off-white">Business Cards</span></li>
                    <li class="mb-2"><span class="text-off-white">Web Design</span></li>
                    <li class="mb-2"><span class="text-off-white">Marketing & SEO</span></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="text-cyan mb-3">Contact Info</h6>
                <div class="mb-2">
                    <i class="fas fa-map-marker-alt text-magenta me-2"></i>
                    <span class="text-off-white">Detroit, MI</span>
                </div>
                <div class="mb-2">
                    <i class="fas fa-phone text-yellow me-2"></i>
                    <a href="tel:+1234567890" class="text-off-white text-decoration-none">(*************</a>
                </div>
                <div class="mb-2">
                    <i class="fas fa-envelope text-cyan me-2"></i>
                    <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-off-white text-decoration-none"><?php echo SITE_EMAIL; ?></a>
                </div>
                <div class="mb-3">
                    <i class="fas fa-clock text-magenta me-2"></i>
                    <span class="text-off-white">Mon-Fri: 9AM-6PM</span>
                </div>

                <!-- Newsletter Signup -->
                <div class="mt-3">
                    <h6 class="text-yellow mb-2">Newsletter</h6>
                    <form class="d-flex" id="footerNewsletterForm">
                        <input type="email" name="email" class="form-control form-control-sm bg-dark-grey-2 border-dark-grey-3 text-white"
                               placeholder="Your email" required>
                        <button class="btn btn-cyan btn-sm ms-2" type="submit">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                    <div class="form-text text-off-white small mt-1">
                        Join our Detroit style community
                    </div>
                </div>
            </div>
        </div>

        <!-- Divider -->
        <hr class="border-dark-grey-3 my-4">

        <!-- Bottom Footer -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-off-white">
                    &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="d-flex justify-content-md-end gap-3">
                    <a href="<?php echo SITE_URL; ?>/privacy.php" class="text-off-white text-decoration-none small">Privacy Policy</a>
                    <a href="<?php echo SITE_URL; ?>/terms.php" class="text-off-white text-decoration-none small">Terms of Service</a>
                    <a href="<?php echo SITE_URL; ?>/sitemap.php" class="text-off-white text-decoration-none small">Sitemap</a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="backToTop" class="btn btn-cyan position-fixed bottom-0 end-0 m-4 rounded-circle"
        style="display: none; z-index: 1000; opacity: 0; transition: all 0.3s ease; width: 50px; height: 50px;">
    <i class="fas fa-chevron-up"></i>
</button>

<style>
#backToTop:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
}
</style>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- FancyBox JS -->
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>

<!-- Frontend AJAX Framework -->
<script src="<?php echo SITE_URL; ?>/assets/js/frontend-ajax.js"></script>

<!-- Working Cart System -->
<script>
// Simple working cart system with localStorage persistence
let cart = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');

function addToCart(productId, productName, productPrice) {
    const item = {
        id: productId,
        name: productName,
        price: parseFloat(productPrice),
        quantity: 1
    };

    const existingItem = cart.find(i => i.id === productId);
    if (existingItem) {
        existingItem.quantity++;
    } else {
        cart.push(item);
    }

    // Save to localStorage
    localStorage.setItem('cyptshop_cart', JSON.stringify(cart));

    updateCartDisplay();
    openCartSidebar();
    showCartNotification(`${productName} added to cart!`);
}

function updateCartDisplay() {
    const count = cart.reduce((total, item) => total + item.quantity, 0);
    const cartCount = document.getElementById('cartCount');
    const cartCountBadge = document.getElementById('cartCountBadge');

    if (cartCount) {
        cartCount.textContent = count;
        cartCount.style.display = count > 0 ? 'inline' : 'none';
    }
    if (cartCountBadge) {
        cartCountBadge.textContent = count;
    }

    updateCartSidebar();
}

function updateCartSidebar() {
    const cartItems = document.getElementById('cartItems');
    const emptyMessage = document.getElementById('emptyCartMessage');
    const cartFooter = document.querySelector('.cart-sidebar-footer');

    if (!cartItems) return;

    if (cart.length === 0) {
        cartItems.innerHTML = '';
        if (emptyMessage) emptyMessage.style.display = 'block';
        if (cartFooter) cartFooter.style.display = 'none';
        return;
    }

    if (emptyMessage) emptyMessage.style.display = 'none';
    if (cartFooter) cartFooter.style.display = 'block';

    cartItems.innerHTML = cart.map(item => `
        <div class="cart-item bg-dark-grey-2 rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-white mb-1">${item.name}</h6>
                    <div class="text-cyan">$${item.price.toFixed(2)} x ${item.quantity}</div>
                </div>
                <div class="text-right">
                    <div class="text-yellow fw-bold">$${(item.price * item.quantity).toFixed(2)}</div>
                    <button class="btn btn-sm btn-outline-danger mt-1" onclick="removeFromCart('${item.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const cartTotal = document.getElementById('cartTotal');
    if (cartTotal) cartTotal.textContent = `$${total.toFixed(2)}`;
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    localStorage.setItem('cyptshop_cart', JSON.stringify(cart));
    updateCartDisplay();
}

function clearCart() {
    cart = [];
    localStorage.setItem('cyptshop_cart', JSON.stringify(cart));
    updateCartDisplay();
    showCartNotification('Cart cleared');
}

function proceedToCheckout() {
    if (cart.length === 0) {
        showCartNotification('Your cart is empty');
        return;
    }
    // Close cart sidebar first
    closeCartSidebar();
    // Redirect to checkout page
    window.location.href = '/checkout/';
}

function openCartSidebar() {
    const sidebar = document.getElementById('cartSidebar');
    const overlay = document.getElementById('cartSidebarOverlay');

    if (sidebar && overlay) {
        sidebar.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
        updateCartSidebar();
    }
}

function closeCartSidebar() {
    const sidebar = document.getElementById('cartSidebar');
    const overlay = document.getElementById('cartSidebarOverlay');

    if (sidebar && overlay) {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }
}

function toggleCartSidebar() {
    const sidebar = document.getElementById('cartSidebar');
    if (sidebar && sidebar.classList.contains('active')) {
        closeCartSidebar();
    } else {
        openCartSidebar();
    }
}

function showCartNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--cyan);
        color: black;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(0,255,255,0.3);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 2000);
}

// Setup event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add to cart buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.add-to-cart')) {
            e.preventDefault();
            const button = e.target.closest('.add-to-cart');
            const productId = button.dataset.productId;
            const productName = button.dataset.productName;
            const productPrice = button.dataset.productPrice;

            if (productId && productName && productPrice) {
                addToCart(productId, productName, productPrice);

                // Visual feedback
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check me-1"></i>Added!';
                button.classList.add('btn-success');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                }, 2000);
            }
        }
    });

    updateCartDisplay();

    // Back to Top Button Functionality
    const backToTopButton = document.getElementById('backToTop');

    // Show/hide button based on scroll position
    function toggleBackToTopButton() {
        if (window.pageYOffset > 300) {
            backToTopButton.style.display = 'block';
            backToTopButton.style.opacity = '1';
        } else {
            backToTopButton.style.opacity = '0';
            setTimeout(() => {
                if (window.pageYOffset <= 300) {
                    backToTopButton.style.display = 'none';
                }
            }, 300);
        }
    }

    // Smooth scroll to top
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Event listeners
    window.addEventListener('scroll', toggleBackToTopButton);
    backToTopButton.addEventListener('click', scrollToTop);

    // Initial check
    toggleBackToTopButton();
});
</script>

<!-- Custom JS -->
<script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>

<!-- Page-specific JavaScript -->
<?php if (isset($pageJS)): ?>
    <script src="<?php echo SITE_URL; ?>/assets/js/<?php echo $pageJS; ?>"></script>
<?php endif; ?>

<!-- Inline JavaScript for this page -->
<?php if (isset($inlineJS)): ?>
    <script>
        <?php echo $inlineJS; ?>
    </script>
<?php endif; ?>

</body>
</html>
