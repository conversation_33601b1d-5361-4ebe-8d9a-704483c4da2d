<?php
/**
 * Common Header Template
 * CYPTSHOP - Task 3.1.1.1: Header Component
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Ensure session is safely started
safeSessionStart();

// Get current user if logged in
$currentUser = getCurrentUser();
$isLoggedIn = isLoggedIn();
$isAdmin = isAdmin();

// Get cart item count (will implement with cart system)
$cartItemCount = isset($_SESSION['cart']) ? count($_SESSION['cart']) : 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- FontAwesome CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- FancyBox CSS -->
    <link href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" rel="stylesheet">

    <!-- Dynamic Theme CSS -->
    <link href="<?php echo SITE_URL; ?>/theme.css.php" rel="stylesheet">

    <!-- Custom CSS with CMYK Theme -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/site/favicon.ico">

    <!-- Meta Tags -->
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'CYPTSHOP - Detroit-style custom T-shirts, apparel design, and print services'; ?>">
    <meta name="keywords" content="custom t-shirts, Detroit design, apparel, print services, graphic design">
    <meta name="author" content="CYPTSHOP">

    <!-- Open Graph Tags -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Detroit-style custom design shop'; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/site/og-image.jpg">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:type" content="website">
</head>
<body class="<?php echo isset($bodyClass) ? $bodyClass : ''; ?>">

<!-- Navigation Header -->
<header class="navbar navbar-expand-lg navbar-dark bg-black fixed-top">
    <div class="container">
        <!-- Logo/Brand -->
        <a class="navbar-brand fw-bold text-cyan" href="<?php echo SITE_URL; ?>">
            <i class="fas fa-tshirt me-2"></i>
            CYPTSHOP
        </a>

        <!-- Mobile Menu Toggle -->
        <button class="navbar-toggler border-cyan" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active text-cyan' : 'text-white'; ?>" href="<?php echo SITE_URL; ?>">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/shop') !== false ? 'active text-primary' : 'text-gray-700'; ?>" href="<?php echo SITE_URL; ?>/shop/">
                        <i class="fas fa-shopping-bag me-1"></i>Shop
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/services') !== false ? 'active text-primary' : 'text-gray-700'; ?>" href="<?php echo SITE_URL; ?>/services/">
                        <i class="fas fa-cogs me-1"></i>Services
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/portfolio') !== false ? 'active text-primary' : 'text-gray-700'; ?>" href="<?php echo SITE_URL; ?>/portfolio/">
                        <i class="fas fa-images me-1"></i>Portfolio
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/contact') !== false ? 'active text-primary' : 'text-gray-700'; ?>" href="<?php echo SITE_URL; ?>/contact/">
                        <i class="fas fa-envelope me-1"></i>Contact
                    </a>
                </li>
            </ul>

            <!-- Right Side Navigation -->
            <ul class="navbar-nav">
                <!-- Cart -->
                <li class="nav-item">
                    <button class="nav-link text-gray-700 position-relative btn btn-link border-0 p-2 cart-trigger"
                            id="cartTrigger"
                            onclick="toggleCartSidebar()">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary"
                              id="cartCount"
                              style="display: none;">
                            0
                        </span>
                    </button>
                </li>

                <!-- User Account -->
                <?php if ($isLoggedIn): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($currentUser['username']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end bg-dark-grey-1 border-cyan">
                            <?php if ($isAdmin): ?>
                                <li><a class="dropdown-item text-white" href="<?php echo SITE_URL; ?>/admin/"><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</a></li>
                                <li><hr class="dropdown-divider border-dark-grey-3"></li>
                            <?php endif; ?>
                            <li><a class="dropdown-item text-white" href="<?php echo SITE_URL; ?>/account/profile.php"><i class="fas fa-user-circle me-2"></i>My Profile</a></li>
                            <li><a class="dropdown-item text-white" href="<?php echo SITE_URL; ?>/account/orders.php"><i class="fas fa-box me-2"></i>My Orders</a></li>
                            <li><hr class="dropdown-divider border-dark-grey-3"></li>
                            <li><a class="dropdown-item text-white" href="<?php echo SITE_URL; ?>/account/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link text-gray-700" href="<?php echo SITE_URL; ?>/account/login/">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-primary" href="<?php echo SITE_URL; ?>/account/register/">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</header>

<!-- Add padding to account for fixed header -->
<div style="padding-top: 76px;"></div>

<?php
// Include cart system
require_once BASE_PATH . 'includes/cart.php';
require_once BASE_PATH . 'includes/cart-sidebar.php';
?>
