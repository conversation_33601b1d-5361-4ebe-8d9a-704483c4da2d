<?php
/**
 * MySQL COMPREHENSIVE Debug Script
 * Find MySQL socket, PDO issues, and service status - MYSQL ONLY SOLUTION
 */

echo "<h1>🔍 COMPREHENSIVE MySQL Diagnostic Script</h1>\n";
echo "<h2>Finding MySQL Socket, PDO, and Service Issues</h2>\n";
echo "<pre>\n";

// Test 1: COMPREHENSIVE MySQL Service Detection
echo "=== 🔍 STEP 1: MySQL Service Detection ===\n";

// Check multiple service names
$services = ['mysql', 'mariadb', 'mysqld', 'mariadb-server', 'mysql-server'];
$active_service = null;

foreach ($services as $service) {
    $status = trim(shell_exec("systemctl is-active $service 2>/dev/null"));
    $enabled = trim(shell_exec("systemctl is-enabled $service 2>/dev/null"));
    echo "Service '$service': Status=$status, Enabled=$enabled\n";

    if ($status === 'active') {
        $active_service = $service;
        echo "✅ FOUND ACTIVE SERVICE: $service\n";
    }
}

if (!$active_service) {
    echo "❌ NO ACTIVE MySQL SERVICE FOUND!\n";
    echo "🔧 ATTEMPTING TO START MySQL SERVICES...\n";

    foreach ($services as $service) {
        $start_result = shell_exec("sudo systemctl start $service 2>&1");
        $status = trim(shell_exec("systemctl is-active $service 2>/dev/null"));
        echo "Attempted to start '$service': $status\n";
        if ($status === 'active') {
            $active_service = $service;
            echo "✅ SUCCESSFULLY STARTED: $service\n";
            break;
        }
    }
}

echo "\n";

// Test 2: FIND MySQL Socket Files
echo "=== 🔍 STEP 2: MySQL Socket File Detection ===\n";

$socket_locations = [
    '/var/run/mysqld/mysqld.sock',
    '/tmp/mysql.sock',
    '/var/lib/mysql/mysql.sock',
    '/run/mysqld/mysqld.sock',
    '/var/run/mysql/mysql.sock',
    '/opt/lampp/var/mysql/mysql.sock'
];

$found_sockets = [];
foreach ($socket_locations as $socket) {
    if (file_exists($socket)) {
        $found_sockets[] = $socket;
        echo "✅ FOUND SOCKET: $socket\n";
    } else {
        echo "❌ NOT FOUND: $socket\n";
    }
}

// Search for socket files
echo "\n🔍 SEARCHING FOR ALL .sock FILES:\n";
$sock_search = shell_exec('find /var /tmp /run -name "*.sock" 2>/dev/null | grep -i mysql');
if ($sock_search) {
    echo "Found socket files:\n$sock_search\n";
} else {
    echo "No MySQL socket files found in standard locations.\n";
}

// Check MySQL configuration for socket location
echo "\n🔍 CHECKING MySQL CONFIGURATION FOR SOCKET:\n";
$mysql_config = shell_exec('mysql --help 2>/dev/null | grep socket');
if ($mysql_config) {
    echo "MySQL socket config:\n$mysql_config\n";
}

// Test 3: MySQL Version and Installation
echo "\n=== 🔍 STEP 3: MySQL Installation Check ===\n";
$mysql_version = shell_exec('mysql --version 2>/dev/null');
echo "MySQL Version: " . trim($mysql_version) . "\n";

$mysqld_version = shell_exec('mysqld --version 2>/dev/null');
echo "MySQLd Version: " . trim($mysqld_version) . "\n";

$which_mysql = shell_exec('which mysql 2>/dev/null');
echo "MySQL Binary Location: " . trim($which_mysql) . "\n";

$which_mysqld = shell_exec('which mysqld 2>/dev/null');
echo "MySQLd Binary Location: " . trim($which_mysqld) . "\n\n";

// Test 4: COMPREHENSIVE PDO Diagnostics
echo "=== 🔍 STEP 4: PDO and PHP MySQL Extensions ===\n";

// Check PDO availability
if (class_exists('PDO')) {
    echo "✅ PDO Class: Available\n";
    $drivers = PDO::getAvailableDrivers();
    echo "Available PDO drivers: " . implode(', ', $drivers) . "\n";

    if (in_array('mysql', $drivers)) {
        echo "✅ PDO MySQL Driver: Available\n";
    } else {
        echo "❌ PDO MySQL Driver: NOT AVAILABLE\n";
        echo "🔧 INSTALL COMMAND: sudo apt-get install php-mysql\n";
    }
} else {
    echo "❌ PDO Class: NOT AVAILABLE\n";
    echo "🔧 INSTALL COMMAND: sudo apt-get install php-pdo\n";
}

// Check MySQL extension
if (extension_loaded('mysql')) {
    echo "✅ MySQL Extension: Available\n";
} else {
    echo "❌ MySQL Extension: NOT AVAILABLE\n";
}

// Check MySQLi extension
if (extension_loaded('mysqli')) {
    echo "✅ MySQLi Extension: Available\n";
} else {
    echo "❌ MySQLi Extension: NOT AVAILABLE\n";
    echo "🔧 INSTALL COMMAND: sudo apt-get install php-mysqli\n";
}

// Check PDO MySQL extension specifically
if (extension_loaded('pdo_mysql')) {
    echo "✅ PDO MySQL Extension: Available\n";
} else {
    echo "❌ PDO MySQL Extension: NOT AVAILABLE\n";
    echo "🔧 INSTALL COMMAND: sudo apt-get install php-mysql\n";
}

// PHP Version and modules
echo "\nPHP Version: " . phpversion() . "\n";
echo "Loaded Extensions: " . implode(', ', get_loaded_extensions()) . "\n\n";

// Test 5: COMPREHENSIVE Connection Testing
echo "=== 🔍 STEP 5: MySQL Connection Testing ===\n";

// Build connection configs dynamically
$connection_configs = [
    [
        'name' => 'Root with empty password (localhost)',
        'host' => 'localhost',
        'user' => 'root',
        'pass' => '',
        'port' => '3306'
    ],
    [
        'name' => 'Root with empty password (127.0.0.1)',
        'host' => '127.0.0.1',
        'user' => 'root',
        'pass' => '',
        'port' => '3306'
    ]
];

// Add socket connections for each found socket
foreach ($found_sockets as $socket) {
    $connection_configs[] = [
        'name' => "Root via socket: $socket",
        'socket' => $socket,
        'user' => 'root',
        'pass' => ''
    ];
}

// Add common password attempts
$passwords = ['root', 'password', 'mysql', 'admin', '123456'];
foreach ($passwords as $pass) {
    $connection_configs[] = [
        'name' => "Root with password '$pass'",
        'host' => 'localhost',
        'user' => 'root',
        'pass' => $pass,
        'port' => '3306'
    ];
}

$working_config = null;

foreach ($connection_configs as $config) {
    echo "🔧 Testing: " . $config['name'] . "\n";

    try {
        // Build DSN based on config
        if (isset($config['socket'])) {
            $dsn = "mysql:unix_socket={$config['socket']};charset=utf8mb4";
        } else {
            $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        }

        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];

        $pdo = new PDO($dsn, $config['user'], $config['pass'], $options);
        echo "✅ CONNECTION SUCCESS!\n";

        // Test database operations
        try {
            // Test basic query
            $result = $pdo->query("SELECT VERSION() as version");
            $version = $result->fetch();
            echo "✅ MySQL Version: " . $version['version'] . "\n";

            // Test database creation
            $pdo->exec("CREATE DATABASE IF NOT EXISTS dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ Database creation: SUCCESS\n";

            // Test database selection
            $pdo->exec("USE dtf_gang_builder");
            echo "✅ Database selection: SUCCESS\n";

            // Test table creation
            $pdo->exec("CREATE TABLE IF NOT EXISTS test_connection (id INT AUTO_INCREMENT PRIMARY KEY, test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");
            echo "✅ Table creation: SUCCESS\n";

            // Test insert
            $pdo->exec("INSERT INTO test_connection VALUES (NULL, NOW())");
            echo "✅ Insert operation: SUCCESS\n";

            // Clean up
            $pdo->exec("DROP TABLE test_connection");
            echo "✅ Cleanup: SUCCESS\n";

        } catch (PDOException $e) {
            echo "⚠️  Database operations warning: " . $e->getMessage() . "\n";
        }

        $working_config = $config;
        echo "\n🎯 *** WORKING CONFIGURATION FOUND! ***\n";
        echo "DSN: $dsn\n";
        echo "User: " . $config['user'] . "\n";
        echo "Password: " . ($config['pass'] ? '[SET: ' . $config['pass'] . ']' : '[EMPTY]') . "\n";
        if (isset($config['socket'])) {
            echo "Socket: " . $config['socket'] . "\n";
        } else {
            echo "Host: " . $config['host'] . "\n";
            echo "Port: " . $config['port'] . "\n";
        }
        echo "*** CONFIGURATION READY FOR USE! ***\n\n";
        break;

    } catch (PDOException $e) {
        echo "❌ FAILED: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Test 5: Check MySQL users and authentication
echo "=== TEST 5: MySQL User Information ===\n";
echo "Attempting to get MySQL user info via command line...\n";

$mysql_users = shell_exec('mysql -e "SELECT user, host, plugin FROM mysql.user WHERE user=\'root\';" 2>/dev/null');
if ($mysql_users) {
    echo "MySQL Root Users:\n";
    echo $mysql_users . "\n";
} else {
    echo "Could not retrieve MySQL user information via command line.\n";
}

// Test 6: Try connecting via command line
echo "=== TEST 6: Command Line Connection Test ===\n";
$cmd_test = shell_exec('mysql -u root -e "SELECT VERSION();" 2>&1');
echo "Command line test result:\n";
echo $cmd_test . "\n";

// Test 7: Check for auth_socket plugin
echo "=== TEST 7: Authentication Plugin Check ===\n";
$auth_check = shell_exec('mysql -u root -e "SELECT user, host, plugin FROM mysql.user WHERE user=\'root\';" 2>/dev/null');
if ($auth_check) {
    echo "Authentication plugins for root user:\n";
    echo $auth_check . "\n";
    
    if (strpos($auth_check, 'auth_socket') !== false) {
        echo "⚠️  WARNING: Root user is using auth_socket plugin.\n";
        echo "This means you need to connect as the system user that matches the MySQL user.\n";
        echo "Try running this script as the mysql system user or create a new MySQL user.\n";
    }
} else {
    echo "Could not check authentication plugins.\n";
}

// Final Results and Solutions
if ($working_config) {
    echo "=== 🎉 SUCCESS: WORKING MySQL CONFIGURATION FOUND! ===\n";
    echo "Use this configuration in your DTF Gang Builder:\n\n";

    if (isset($working_config['socket'])) {
        echo "// Socket-based connection\n";
        echo "define('DTF_DB_HOST', 'localhost');\n";
        echo "define('DTF_DB_SOCKET', '{$working_config['socket']}');\n";
    } else {
        echo "// Network-based connection\n";
        echo "define('DTF_DB_HOST', '{$working_config['host']}');\n";
        echo "define('DTF_DB_PORT', '{$working_config['port']}');\n";
    }
    echo "define('DTF_DB_USER', '{$working_config['user']}');\n";
    echo "define('DTF_DB_PASS', '{$working_config['pass']}');\n";
    echo "define('DTF_DB_NAME', 'dtf_gang_builder');\n\n";

} else {
    echo "=== ❌ NO WORKING CONFIGURATION FOUND ===\n";
    echo "🔧 MYSQL INSTALLATION AND CONFIGURATION REQUIRED:\n\n";

    echo "1. INSTALL MySQL/MariaDB:\n";
    echo "   sudo apt-get update\n";
    echo "   sudo apt-get install mysql-server mysql-client\n";
    echo "   # OR for MariaDB:\n";
    echo "   sudo apt-get install mariadb-server mariadb-client\n\n";

    echo "2. INSTALL PHP MySQL Extensions:\n";
    echo "   sudo apt-get install php-mysql php-pdo php-mysqli\n";
    echo "   sudo systemctl restart apache2\n";
    echo "   # OR for nginx:\n";
    echo "   sudo systemctl restart php7.4-fpm\n\n";

    echo "3. START MySQL Service:\n";
    echo "   sudo systemctl start mysql\n";
    echo "   sudo systemctl enable mysql\n\n";

    echo "4. SECURE MySQL Installation:\n";
    echo "   sudo mysql_secure_installation\n\n";

    echo "5. CREATE DTF Database User:\n";
    echo "   sudo mysql -u root -p\n";
    echo "   CREATE DATABASE dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
    echo "   CREATE USER 'dtf_user'@'localhost' IDENTIFIED BY 'dtf_strong_password';\n";
    echo "   GRANT ALL PRIVILEGES ON dtf_gang_builder.* TO 'dtf_user'@'localhost';\n";
    echo "   FLUSH PRIVILEGES;\n";
    echo "   EXIT;\n\n";

    echo "6. FIX Root Authentication (if needed):\n";
    echo "   sudo mysql -u root\n";
    echo "   ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_root_password';\n";
    echo "   FLUSH PRIVILEGES;\n\n";
}

echo "=== 🔍 SYSTEM INFORMATION SUMMARY ===\n";
echo "Active MySQL Service: " . ($active_service ?: 'NONE') . "\n";
echo "Found Socket Files: " . (count($found_sockets) > 0 ? implode(', ', $found_sockets) : 'NONE') . "\n";
echo "PDO MySQL Available: " . (in_array('mysql', PDO::getAvailableDrivers()) ? 'YES' : 'NO') . "\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Operating System: " . php_uname() . "\n";

echo "\n=== 🎯 NEXT STEPS ===\n";
if ($working_config) {
    echo "1. Update your DTF Gang Builder config.php with the working configuration above\n";
    echo "2. Test the DTF Gang Builder application\n";
    echo "3. Verify database tables are created successfully\n";
} else {
    echo "1. Follow the installation steps above\n";
    echo "2. Run this diagnostic script again\n";
    echo "3. Update DTF Gang Builder configuration once MySQL is working\n";
}

echo "\n</pre>\n";
?>
