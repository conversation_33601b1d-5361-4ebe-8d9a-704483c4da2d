<?php
/**
 * MySQL Debug Script
 * This script tests various MySQL connection methods to find the working configuration
 */

echo "<h1>MySQL Connection Debug Script</h1>\n";
echo "<pre>\n";

// Test 1: Check if MySQL is running
echo "=== TEST 1: MySQL Service Status ===\n";
$mysql_status = shell_exec('systemctl is-active mysql 2>/dev/null || systemctl is-active mariadb 2>/dev/null');
echo "MySQL/MariaDB Status: " . trim($mysql_status) . "\n\n";

// Test 2: Check MySQL version
echo "=== TEST 2: MySQL Version ===\n";
$mysql_version = shell_exec('mysql --version 2>/dev/null');
echo "MySQL Version: " . trim($mysql_version) . "\n\n";

// Test 3: Check available PDO drivers
echo "=== TEST 3: Available PDO Drivers ===\n";
$drivers = PDO::getAvailableDrivers();
echo "Available PDO drivers: " . implode(', ', $drivers) . "\n\n";

// Test 4: Try different connection methods
echo "=== TEST 4: Connection Attempts ===\n";

$connection_configs = [
    [
        'name' => 'Root with empty password',
        'host' => 'localhost',
        'user' => 'root',
        'pass' => '',
        'port' => '3306'
    ],
    [
        'name' => 'Root with root password',
        'host' => 'localhost',
        'user' => 'root',
        'pass' => 'root',
        'port' => '3306'
    ],
    [
        'name' => 'Root with password password',
        'host' => 'localhost',
        'user' => 'root',
        'pass' => 'password',
        'port' => '3306'
    ],
    [
        'name' => 'Root via socket',
        'host' => 'localhost',
        'user' => 'root',
        'pass' => '',
        'port' => '3306',
        'socket' => true
    ],
    [
        'name' => 'Root via 127.0.0.1',
        'host' => '127.0.0.1',
        'user' => 'root',
        'pass' => '',
        'port' => '3306'
    ]
];

foreach ($connection_configs as $config) {
    echo "Testing: " . $config['name'] . "\n";
    
    try {
        if (isset($config['socket']) && $config['socket']) {
            $dsn = "mysql:unix_socket=/var/run/mysqld/mysqld.sock;charset=utf8mb4";
        } else {
            $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        }
        
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
        
        $pdo = new PDO($dsn, $config['user'], $config['pass'], $options);
        echo "✅ SUCCESS: Connected successfully!\n";
        
        // Test creating database
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ Database creation: SUCCESS\n";
            
            // Test selecting the database
            $pdo->exec("USE dtf_gang_builder");
            echo "✅ Database selection: SUCCESS\n";
            
            // Test creating a simple table
            $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255))");
            echo "✅ Table creation: SUCCESS\n";
            
            // Clean up test table
            $pdo->exec("DROP TABLE IF EXISTS test_table");
            echo "✅ Table cleanup: SUCCESS\n";
            
        } catch (PDOException $e) {
            echo "❌ Database operations failed: " . $e->getMessage() . "\n";
        }
        
        echo "🎯 WORKING CONFIGURATION FOUND!\n";
        echo "Host: " . $config['host'] . "\n";
        echo "User: " . $config['user'] . "\n";
        echo "Password: " . ($config['pass'] ? '[SET]' : '[EMPTY]') . "\n";
        echo "Port: " . $config['port'] . "\n";
        if (isset($config['socket'])) {
            echo "Socket: YES\n";
        }
        break;
        
    } catch (PDOException $e) {
        echo "❌ FAILED: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Test 5: Check MySQL users and authentication
echo "=== TEST 5: MySQL User Information ===\n";
echo "Attempting to get MySQL user info via command line...\n";

$mysql_users = shell_exec('mysql -e "SELECT user, host, plugin FROM mysql.user WHERE user=\'root\';" 2>/dev/null');
if ($mysql_users) {
    echo "MySQL Root Users:\n";
    echo $mysql_users . "\n";
} else {
    echo "Could not retrieve MySQL user information via command line.\n";
}

// Test 6: Try connecting via command line
echo "=== TEST 6: Command Line Connection Test ===\n";
$cmd_test = shell_exec('mysql -u root -e "SELECT VERSION();" 2>&1');
echo "Command line test result:\n";
echo $cmd_test . "\n";

// Test 7: Check for auth_socket plugin
echo "=== TEST 7: Authentication Plugin Check ===\n";
$auth_check = shell_exec('mysql -u root -e "SELECT user, host, plugin FROM mysql.user WHERE user=\'root\';" 2>/dev/null');
if ($auth_check) {
    echo "Authentication plugins for root user:\n";
    echo $auth_check . "\n";
    
    if (strpos($auth_check, 'auth_socket') !== false) {
        echo "⚠️  WARNING: Root user is using auth_socket plugin.\n";
        echo "This means you need to connect as the system user that matches the MySQL user.\n";
        echo "Try running this script as the mysql system user or create a new MySQL user.\n";
    }
} else {
    echo "Could not check authentication plugins.\n";
}

// Test 8: Suggest solutions
echo "=== TEST 8: Suggested Solutions ===\n";
echo "If all connections failed, try these solutions:\n\n";

echo "1. Create a new MySQL user:\n";
echo "   sudo mysql -u root\n";
echo "   CREATE USER 'dtf_user'@'localhost' IDENTIFIED BY 'dtf_password';\n";
echo "   GRANT ALL PRIVILEGES ON *.* TO 'dtf_user'@'localhost';\n";
echo "   FLUSH PRIVILEGES;\n\n";

echo "2. Change root authentication method:\n";
echo "   sudo mysql -u root\n";
echo "   ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'newpassword';\n";
echo "   FLUSH PRIVILEGES;\n\n";

echo "3. Use socket authentication:\n";
echo "   Run PHP as the mysql user or use unix socket connection\n\n";

echo "4. Check MySQL configuration:\n";
echo "   sudo cat /etc/mysql/mysql.conf.d/mysqld.cnf | grep bind-address\n\n";

echo "</pre>\n";
?>
