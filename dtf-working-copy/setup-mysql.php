<?php
/**
 * MySQL Setup Script for DTF Gang Builder
 * This script creates the necessary MySQL user and database
 */

echo "<h1>🔧 MySQL Setup for DTF Gang Builder</h1>\n";
echo "<pre>\n";

echo "=== 🎯 MYSQL SETUP PROCESS ===\n\n";

echo "This script will help you set up MySQL for the DTF Gang Builder.\n";
echo "The issue is that MariaDB root user is using auth_socket authentication.\n\n";

echo "=== 📋 MULTIPLE SETUP OPTIONS ===\n\n";

echo "🎯 OPTION 1: Using SQL File (RECOMMENDED)\n";
echo "   1. Download the SQL file: <a href='create-mysql-user.sql' download>create-mysql-user.sql</a>\n";
echo "   2. Run in terminal: sudo mysql < create-mysql-user.sql\n";
echo "   3. Test: mysql -u dtf_user -p dtf_gang_builder\n\n";

echo "🎯 OPTION 2: Manual Commands\n";
echo "   Run these commands in your terminal:\n\n";
echo "   # Connect to MySQL\n";
echo "   sudo mysql\n\n";
echo "   # Copy and paste these SQL commands:\n";
echo "   CREATE DATABASE IF NOT EXISTS dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
echo "   CREATE USER IF NOT EXISTS 'dtf_user'@'localhost' IDENTIFIED BY 'dtf_password_2024';\n";
echo "   GRANT ALL PRIVILEGES ON dtf_gang_builder.* TO 'dtf_user'@'localhost';\n";
echo "   FLUSH PRIVILEGES;\n";
echo "   EXIT;\n\n";

echo "🎯 OPTION 3: One-Line Command\n";
echo "   sudo mysql -e \"CREATE DATABASE IF NOT EXISTS dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; CREATE USER IF NOT EXISTS 'dtf_user'@'localhost' IDENTIFIED BY 'dtf_password_2024'; GRANT ALL PRIVILEGES ON dtf_gang_builder.* TO 'dtf_user'@'localhost'; FLUSH PRIVILEGES;\"\n\n";

echo "🧪 TEST CONNECTION:\n";
echo "   mysql -u dtf_user -p dtf_gang_builder\n";
echo "   (Password: dtf_password_2024)\n\n";

// Test if we can connect with the new user
echo "=== 🔍 TESTING CONNECTION WITH DTF USER ===\n";

try {
    $dsn = 'mysql:host=localhost;port=3306;dbname=dtf_gang_builder;charset=utf8mb4';
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, 'dtf_user', 'dtf_password_2024', $options);
    
    // Test the connection
    $result = $pdo->query("SELECT VERSION() as version, DATABASE() as database");
    $info = $result->fetch();
    
    echo "✅ SUCCESS: Connected to MySQL!\n";
    echo "   MySQL Version: " . $info['version'] . "\n";
    echo "   Database: " . $info['database'] . "\n\n";
    
    // Test table creation
    $pdo->exec("CREATE TABLE IF NOT EXISTS test_dtf_connection (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        message VARCHAR(255)
    )");
    
    $pdo->exec("INSERT INTO test_dtf_connection (message) VALUES ('DTF Gang Builder connection test')");
    
    $test_result = $pdo->query("SELECT * FROM test_dtf_connection ORDER BY id DESC LIMIT 1");
    $test_data = $test_result->fetch();
    
    echo "✅ DATABASE OPERATIONS: Working!\n";
    echo "   Test Record ID: " . $test_data['id'] . "\n";
    echo "   Test Time: " . $test_data['test_time'] . "\n";
    echo "   Message: " . $test_data['message'] . "\n\n";
    
    // Clean up test table
    $pdo->exec("DROP TABLE test_dtf_connection");
    echo "✅ CLEANUP: Test table removed\n\n";
    
    echo "🎉 MYSQL SETUP SUCCESSFUL!\n\n";
    
    // Update the configuration file
    echo "=== 🔧 UPDATING DTF CONFIGURATION ===\n";
    
    $config_file = __DIR__ . '/includes/config.php';
    $config_content = file_get_contents($config_file);
    
    // Update database credentials
    $config_content = preg_replace(
        "/define\('DTF_DB_USER', '[^']*'\);/",
        "define('DTF_DB_USER', 'dtf_user');",
        $config_content
    );
    
    $config_content = preg_replace(
        "/define\('DTF_DB_PASS', '[^']*'\);/",
        "define('DTF_DB_PASS', 'dtf_password_2024');",
        $config_content
    );
    
    if (file_put_contents($config_file, $config_content)) {
        echo "✅ Configuration updated successfully!\n";
        echo "   User: dtf_user\n";
        echo "   Password: dtf_password_2024\n";
        echo "   Database: dtf_gang_builder\n\n";
    } else {
        echo "❌ Failed to update configuration file\n";
        echo "   Please manually update includes/config.php:\n";
        echo "   DTF_DB_USER: 'dtf_user'\n";
        echo "   DTF_DB_PASS: 'dtf_password_2024'\n\n";
    }
    
    echo "=== 🎯 NEXT STEPS ===\n";
    echo "1. ✅ MySQL user created and tested\n";
    echo "2. ✅ Database connection working\n";
    echo "3. ✅ Configuration updated\n";
    echo "4. 🚀 Ready to use DTF Gang Builder!\n\n";
    
    echo "🔗 Test the DTF Gang Builder:\n";
    echo "   <a href='professional-builder.php'>Open DTF Gang Builder</a>\n\n";
    
} catch (PDOException $e) {
    echo "❌ CONNECTION FAILED: " . $e->getMessage() . "\n\n";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "🔧 SOLUTION: The dtf_user doesn't exist yet.\n";
        echo "   Please run the MySQL commands above to create the user.\n\n";
    } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "🔧 SOLUTION: The database doesn't exist yet.\n";
        echo "   Please run the MySQL commands above to create the database.\n\n";
    } else {
        echo "🔧 SOLUTION: Check MySQL service and configuration.\n\n";
    }
    
    echo "=== 🛠️ MANUAL SETUP COMMANDS ===\n\n";
    echo "Run these commands in your terminal:\n\n";
    
    echo "# 1. Connect to MySQL\n";
    echo "sudo mysql\n\n";
    
    echo "# 2. Create database and user\n";
    echo "CREATE DATABASE IF NOT EXISTS dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
    echo "CREATE USER 'dtf_user'@'localhost' IDENTIFIED BY 'dtf_password_2024';\n";
    echo "GRANT ALL PRIVILEGES ON dtf_gang_builder.* TO 'dtf_user'@'localhost';\n";
    echo "FLUSH PRIVILEGES;\n";
    echo "SHOW DATABASES;\n";
    echo "SELECT user, host FROM mysql.user WHERE user='dtf_user';\n";
    echo "EXIT;\n\n";
    
    echo "# 3. Test the connection\n";
    echo "mysql -u dtf_user -p dtf_gang_builder\n";
    echo "# Enter password: dtf_password_2024\n\n";
    
    echo "# 4. Run this setup script again\n";
    echo "# Refresh this page after creating the user\n\n";
}

echo "=== 📊 SYSTEM STATUS ===\n";
echo "MariaDB Service: " . trim(shell_exec('systemctl is-active mariadb 2>/dev/null')) . "\n";
echo "MySQL Socket: " . (file_exists('/run/mysqld/mysqld.sock') ? '✅ Found' : '❌ Not found') . "\n";
echo "PDO MySQL: " . (in_array('mysql', PDO::getAvailableDrivers()) ? '✅ Available' : '❌ Not available') . "\n";
echo "PHP Version: " . phpversion() . "\n";

echo "\n</pre>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
h1 { color: #2c3e50; }
a { color: #3498db; text-decoration: none; font-weight: bold; }
a:hover { text-decoration: underline; }
</style>
