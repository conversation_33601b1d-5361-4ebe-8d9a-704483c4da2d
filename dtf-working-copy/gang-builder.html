<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Sheet Builder - Simple & Fast</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main {
            padding: 40px;
        }

        .step {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .step.active {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-title {
            display: inline-block;
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            vertical-align: top;
            margin-top: 5px;
        }

        /* Upload Zone */
        .upload-zone {
            border: 3px dashed #ccc;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .upload-zone:hover, .upload-zone.dragover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .upload-zone.has-image {
            border-color: #28a745;
            background: #f8fff9;
        }

        .upload-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #999;
        }

        .file-input { display: none; }

        /* Controls */
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .control-group {
            text-align: center;
        }

        .control-label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .control-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1.1rem;
            text-align: center;
            transition: border-color 0.3s ease;
        }

        .control-input:focus {
            outline: none;
            border-color: #667eea;
        }

        /* Preview */
        .preview-container {
            margin-top: 20px;
            text-align: center;
        }

        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Canvas */
        .canvas-container {
            text-align: center;
            margin-top: 20px;
        }

        #gang-canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            max-width: 100%;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            margin: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .hidden { display: none; }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
            font-weight: 600;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .controls { grid-template-columns: 1fr; }
            .main { padding: 20px; }
            .header h1 { font-size: 2rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 DTF Gang Sheet Builder</h1>
            <p>Upload your image → Choose quantity → Download gang sheet</p>
        </div>

        <div class="main">
            <!-- Step 1: Upload -->
            <div class="step active" id="step1">
                <div class="step-header">
                    <span class="step-number">1</span>
                    <span class="step-title">Upload Your Image</span>
                </div>
                
                <div id="upload-zone" class="upload-zone">
                    <div class="upload-icon">📸</div>
                    <div class="upload-text">Click here or drag your image</div>
                    <div class="upload-subtext">PNG, JPG, JPEG, GIF • Max 10MB</div>
                </div>
                <input type="file" id="file-input" class="file-input" accept="image/*">
                
                <div id="preview-container" class="preview-container hidden">
                    <img id="preview-image" class="preview-image" alt="Preview">
                </div>
            </div>

            <!-- Step 2: Configure -->
            <div class="step" id="step2">
                <div class="step-header">
                    <span class="step-number">2</span>
                    <span class="step-title">Configure Your Gang Sheet</span>
                </div>
                
                <div class="controls">
                    <div class="control-group">
                        <label class="control-label">Sheet Size</label>
                        <select id="sheet-size" class="control-input">
                            <option value="30x12">30" × 12"</option>
                            <option value="30x24">30" × 24"</option>
                            <option value="30x36">30" × 36"</option>
                            <option value="30x48">30" × 48"</option>
                            <option value="30x60">30" × 60"</option>
                            <option value="30x72" selected>30" × 72"</option>
                            <option value="30x100">30" × 100"</option>
                            <option value="30x120">30" × 120"</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Number of Copies</label>
                        <input type="number" id="quantity" class="control-input" value="12" min="1" max="100">
                    </div>
                </div>
            </div>

            <!-- Step 3: Generate -->
            <div class="step" id="step3">
                <div class="step-header">
                    <span class="step-number">3</span>
                    <span class="step-title">Generate & Download</span>
                </div>
                
                <div style="text-align: center;">
                    <button id="generate-btn" class="btn btn-primary" disabled>
                        🚀 Generate Gang Sheet
                    </button>
                    <button id="download-btn" class="btn btn-success hidden">
                        📥 Download PNG
                    </button>
                </div>

                <div class="canvas-container">
                    <canvas id="gang-canvas" width="800" height="600"></canvas>
                </div>

                <div id="status" class="status hidden"></div>
            </div>
        </div>
    </div>

    <script>
        class DTFGangBuilder {
            constructor() {
                this.canvas = document.getElementById('gang-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.image = null;
                this.sheetSize = '30x72';
                this.quantity = 12;
                
                this.init();
            }

            init() {
                this.setupEvents();
                this.drawPlaceholder();
            }

            setupEvents() {
                const uploadZone = document.getElementById('upload-zone');
                const fileInput = document.getElementById('file-input');

                // Upload events
                uploadZone.onclick = () => fileInput.click();
                uploadZone.ondragover = (e) => {
                    e.preventDefault();
                    uploadZone.classList.add('dragover');
                };
                uploadZone.ondragleave = () => uploadZone.classList.remove('dragover');
                uploadZone.ondrop = (e) => {
                    e.preventDefault();
                    uploadZone.classList.remove('dragover');
                    this.handleFile(e.dataTransfer.files[0]);
                };
                fileInput.onchange = (e) => this.handleFile(e.target.files[0]);

                // Controls
                document.getElementById('sheet-size').onchange = (e) => {
                    this.sheetSize = e.target.value;
                    this.updatePreview();
                };
                document.getElementById('quantity').oninput = (e) => {
                    this.quantity = Math.max(1, Math.min(100, parseInt(e.target.value) || 1));
                    this.updatePreview();
                };

                // Actions
                document.getElementById('generate-btn').onclick = () => this.generate();
                document.getElementById('download-btn').onclick = () => this.download();
            }

            handleFile(file) {
                if (!file || !file.type.startsWith('image/')) {
                    this.showStatus('Please select a valid image file', 'error');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    this.showStatus('File too large. Please choose an image under 10MB', 'error');
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        this.image = img;
                        this.showPreview(e.target.result);
                        this.activateStep(2);
                        this.enableGenerate();
                        this.updatePreview();
                        this.showStatus('Image loaded! Configure your gang sheet below.', 'success');
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }

            showPreview(src) {
                const container = document.getElementById('preview-container');
                const img = document.getElementById('preview-image');
                const uploadZone = document.getElementById('upload-zone');
                
                img.src = src;
                container.classList.remove('hidden');
                uploadZone.classList.add('has-image');
                uploadZone.innerHTML = `
                    <div class="upload-icon">✅</div>
                    <div class="upload-text">Image uploaded successfully!</div>
                    <div class="upload-subtext">Click to change image</div>
                `;
            }

            activateStep(stepNumber) {
                document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));
                document.getElementById(`step${stepNumber}`).classList.add('active');
            }

            enableGenerate() {
                document.getElementById('generate-btn').disabled = false;
            }

            drawPlaceholder() {
                this.ctx.fillStyle = '#f8f9fa';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                this.ctx.strokeStyle = '#ddd';
                this.ctx.setLineDash([10, 10]);
                this.ctx.strokeRect(20, 20, this.canvas.width - 40, this.canvas.height - 40);
                
                this.ctx.fillStyle = '#999';
                this.ctx.font = '24px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('Your gang sheet will appear here', this.canvas.width / 2, this.canvas.height / 2);
            }

            updatePreview() {
                if (!this.image) return;
                
                // Clear canvas
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Calculate layout
                const layout = this.calculateLayout();
                
                // Draw images
                layout.positions.forEach((pos, index) => {
                    if (index < this.quantity) {
                        this.ctx.drawImage(
                            this.image,
                            pos.x, pos.y,
                            layout.imageWidth, layout.imageHeight
                        );
                    }
                });

                // Draw grid lines
                this.ctx.strokeStyle = '#e0e0e0';
                this.ctx.setLineDash([2, 2]);
                this.ctx.lineWidth = 1;
                
                for (let i = 0; i <= layout.cols; i++) {
                    const x = i * layout.cellWidth;
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, this.canvas.height);
                    this.ctx.stroke();
                }
                
                for (let i = 0; i <= layout.rows; i++) {
                    const y = i * layout.cellHeight;
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(this.canvas.width, y);
                    this.ctx.stroke();
                }
            }

            calculateLayout() {
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;
                
                // Calculate grid dimensions
                const cols = Math.ceil(Math.sqrt(this.quantity * (canvasWidth / canvasHeight)));
                const rows = Math.ceil(this.quantity / cols);
                
                const cellWidth = canvasWidth / cols;
                const cellHeight = canvasHeight / rows;
                
                // Calculate image size (with padding)
                const padding = 5;
                const imageWidth = cellWidth - (padding * 2);
                const imageHeight = cellHeight - (padding * 2);
                
                // Generate positions
                const positions = [];
                for (let i = 0; i < this.quantity; i++) {
                    const col = i % cols;
                    const row = Math.floor(i / cols);
                    positions.push({
                        x: (col * cellWidth) + padding,
                        y: (row * cellHeight) + padding
                    });
                }

                return { positions, imageWidth, imageHeight, cols, rows, cellWidth, cellHeight };
            }

            generate() {
                if (!this.image) {
                    this.showStatus('Please upload an image first', 'error');
                    return;
                }

                this.activateStep(3);
                this.updatePreview();
                
                document.getElementById('download-btn').classList.remove('hidden');
                this.showStatus(`Gang sheet generated with ${this.quantity} copies!`, 'success');
            }

            download() {
                const link = document.createElement('a');
                link.download = `dtf-gang-sheet-${this.sheetSize}-${this.quantity}copies.png`;
                link.href = this.canvas.toDataURL('image/png', 1.0);
                link.click();
                
                this.showStatus('Download started! 🎉', 'success');
            }

            showStatus(message, type) {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');
                
                setTimeout(() => status.classList.add('hidden'), 4000);
            }
        }

        // Start the app
        new DTFGangBuilder();
    </script>
</body>
</html>
