<?php
/**
 * DTF Gang Builder - Database Abstraction Layer
 * Supports both JSON file storage and MySQL with seamless switching
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * Database Abstraction Interface
 */
interface DTF_DatabaseInterface {
    public function connect();
    public function insert($table, $data);
    public function fetch($table, $where = [], $limit = 1);
    public function fetchAll($table, $where = [], $orderBy = null, $limit = null);
    public function update($table, $data, $where);
    public function delete($table, $where);
    public function tableExists($table);
    public function createTables();
}

/**
 * JSON File Database Implementation
 */
class DTF_JsonDatabase implements DTF_DatabaseInterface {
    private $dataPath;
    private $tables = ['users', 'projects', 'images', 'gang_sheets', 'orders', 'logs'];
    
    public function __construct() {
        $this->dataPath = DTF_BASE_PATH . 'data/';
        $this->ensureDataDirectory();
        $this->initializeFiles();
    }
    
    private function ensureDataDirectory() {
        if (!is_dir($this->dataPath)) {
            mkdir($this->dataPath, 0755, true);
        }
    }
    
    private function initializeFiles() {
        foreach ($this->tables as $table) {
            $file = $this->getTableFile($table);
            if (!file_exists($file)) {
                $this->writeJsonFile($file, []);
            }
        }
    }
    
    private function getTableFile($table) {
        return $this->dataPath . DTF_DB_PREFIX . $table . '.json';
    }
    
    private function readJsonFile($file) {
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file_get_contents($file);
        $data = json_decode($content, true);
        return $data ?: [];
    }
    
    private function writeJsonFile($file, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file, $json, LOCK_EX) !== false;
    }
    
    private function generateId($table) {
        $data = $this->readJsonFile($this->getTableFile($table));
        $maxId = 0;
        
        foreach ($data as $record) {
            if (isset($record['id']) && $record['id'] > $maxId) {
                $maxId = $record['id'];
            }
        }
        
        return $maxId + 1;
    }
    
    public function connect() {
        return true; // JSON files don't need connection
    }
    
    public function insert($table, $data) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        
        $data['id'] = $this->generateId($table);
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $records[] = $data;
        
        if ($this->writeJsonFile($file, $records)) {
            return $data['id'];
        }
        
        throw new Exception("Failed to insert record into $table");
    }
    
    public function fetch($table, $where = [], $limit = 1) {
        $results = $this->fetchAll($table, $where, null, $limit);
        return $limit === 1 ? ($results[0] ?? null) : $results;
    }
    
    public function fetchAll($table, $where = [], $orderBy = null, $limit = null) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $results = [];
        
        foreach ($records as $record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                $results[] = $record;
            }
        }
        
        // Simple ordering
        if ($orderBy) {
            $desc = strpos($orderBy, 'DESC') !== false;
            $field = str_replace([' DESC', ' ASC'], '', $orderBy);
            
            usort($results, function($a, $b) use ($field, $desc) {
                $aVal = $a[$field] ?? '';
                $bVal = $b[$field] ?? '';
                
                if ($desc) {
                    return $bVal <=> $aVal;
                } else {
                    return $aVal <=> $bVal;
                }
            });
        }
        
        // Apply limit
        if ($limit) {
            $results = array_slice($results, 0, $limit);
        }
        
        return $results;
    }
    
    public function update($table, $data, $where) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $updated = 0;
        
        foreach ($records as &$record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                foreach ($data as $field => $value) {
                    $record[$field] = $value;
                }
                $record['updated_at'] = date('Y-m-d H:i:s');
                $updated++;
            }
        }
        
        if ($updated > 0) {
            $this->writeJsonFile($file, $records);
        }
        
        return $updated;
    }
    
    public function delete($table, $where) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $newRecords = [];
        $deleted = 0;
        
        foreach ($records as $record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if (!$match) {
                $newRecords[] = $record;
            } else {
                $deleted++;
            }
        }
        
        if ($deleted > 0) {
            $this->writeJsonFile($file, $newRecords);
        }
        
        return $deleted;
    }
    
    public function tableExists($table) {
        return in_array($table, $this->tables);
    }
    
    public function createTables() {
        // Tables are created automatically when first accessed
        return true;
    }
}

/**
 * MySQL Database Implementation
 */
class DTF_MySQLDatabase implements DTF_DatabaseInterface {
    private $connection = null;
    private $options;
    
    public function __construct() {
        $this->options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ];
    }
    
    public function connect() {
        if ($this->connection === null) {
            $connectionAttempts = [
                [
                    'dsn' => 'mysql:host=' . DTF_DB_HOST . ';port=' . DTF_DB_PORT . ';dbname=' . DTF_DB_NAME . ';charset=utf8mb4',
                    'user' => DTF_DB_USER,
                    'pass' => DTF_DB_PASS
                ],
                [
                    'dsn' => 'mysql:unix_socket=' . DTF_DB_SOCKET . ';dbname=' . DTF_DB_NAME . ';charset=utf8mb4',
                    'user' => DTF_DB_USER,
                    'pass' => DTF_DB_PASS
                ]
            ];
            
            foreach ($connectionAttempts as $attempt) {
                try {
                    $this->connection = new PDO($attempt['dsn'], $attempt['user'], $attempt['pass'], $this->options);
                    return true;
                } catch (PDOException $e) {
                    continue;
                }
            }
            
            throw new Exception("MySQL connection failed");
        }
        
        return true;
    }
    
    public function insert($table, $data) {
        $this->connect();
        
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        
        $sql = "INSERT INTO " . DTF_DB_PREFIX . "$table (" . implode(', ', $fields) . ") VALUES ($placeholders)";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($data);
        
        return $this->connection->lastInsertId();
    }
    
    public function fetch($table, $where = [], $limit = 1) {
        $results = $this->fetchAll($table, $where, null, $limit);
        return $limit === 1 ? ($results[0] ?? null) : $results;
    }
    
    public function fetchAll($table, $where = [], $orderBy = null, $limit = null) {
        $this->connect();
        
        $sql = "SELECT * FROM " . DTF_DB_PREFIX . $table;
        $params = [];
        
        if (!empty($where)) {
            $conditions = [];
            foreach ($where as $field => $value) {
                $conditions[] = "$field = :$field";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    public function update($table, $data, $where) {
        $this->connect();
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $setClause = [];
        foreach ($data as $field => $value) {
            $setClause[] = "$field = :set_$field";
        }
        
        $whereClause = [];
        foreach ($where as $field => $value) {
            $whereClause[] = "$field = :where_$field";
        }
        
        $sql = "UPDATE " . DTF_DB_PREFIX . "$table SET " . implode(', ', $setClause) . " WHERE " . implode(' AND ', $whereClause);
        
        $params = [];
        foreach ($data as $field => $value) {
            $params["set_$field"] = $value;
        }
        foreach ($where as $field => $value) {
            $params["where_$field"] = $value;
        }
        
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    }
    
    public function delete($table, $where) {
        $this->connect();
        
        $whereClause = [];
        foreach ($where as $field => $value) {
            $whereClause[] = "$field = :$field";
        }
        
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "$table WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($where);
        
        return $stmt->rowCount();
    }
    
    public function tableExists($table) {
        $this->connect();
        
        $sql = "SHOW TABLES LIKE :table";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute(['table' => DTF_DB_PREFIX . $table]);
        
        return $stmt->rowCount() > 0;
    }
    
    public function createTables() {
        // MySQL table creation would go here
        // For now, return true as tables will be created when needed
        return true;
    }
}

/**
 * Database Factory
 */
class DTF_DatabaseFactory {
    private static $instance = null;

    public static function getInstance() {
        if (self::$instance === null) {
            // Try MySQL first, fall back to JSON
            if (defined('DTF_DB_TYPE') && DTF_DB_TYPE === 'mysql') {
                try {
                    self::$instance = new DTF_MySQLDatabase();
                    self::$instance->connect();

                    if (DTF_DEBUG) {
                        error_log("DTF Gang Builder: Using MySQL database");
                    }
                } catch (Exception $e) {
                    if (DTF_DEBUG) {
                        error_log("DTF Gang Builder: MySQL failed, falling back to JSON - " . $e->getMessage());
                    }
                    self::$instance = new DTF_JsonDatabase();
                }
            } else {
                self::$instance = new DTF_JsonDatabase();

                if (DTF_DEBUG) {
                    error_log("DTF Gang Builder: Using JSON file database");
                }
            }
        }

        return self::$instance;
    }

    public static function reset() {
        self::$instance = null;
    }
}

/**
 * Helper Functions for Database Operations
 */

/**
 * Get database instance
 */
function dtf_db() {
    return DTF_DatabaseFactory::getInstance();
}

/**
 * Get or create user by session
 */
function dtf_get_or_create_user() {
    $db = dtf_db();
    $sessionId = session_id();
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Try to find existing user by session
    $user = $db->fetch('users', ['session_id' => $sessionId]);

    if (!$user) {
        // Create new user
        $userId = $db->insert('users', [
            'session_id' => $sessionId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'email' => null,
            'name' => null,
            'last_activity' => date('Y-m-d H:i:s')
        ]);

        $user = $db->fetch('users', ['id' => $userId]);
    } else {
        // Update last activity
        $db->update('users',
            ['last_activity' => date('Y-m-d H:i:s')],
            ['id' => $user['id']]
        );
    }

    return $user;
}

/**
 * Log DTF events
 */
function dtf_log($level, $message, $context = []) {
    try {
        $db = dtf_db();

        $logData = [
            'level' => strtoupper($level),
            'message' => $message,
            'context' => json_encode($context),
            'user_id' => $_SESSION['dtf_user_id'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];

        $db->insert('logs', $logData);

        // Also log to PHP error log if debug mode
        if (DTF_DEBUG) {
            error_log("DTF Gang Builder [$level]: $message " . json_encode($context));
        }
    } catch (Exception $e) {
        // Fallback to error log if database logging fails
        error_log("DTF Gang Builder [$level]: $message " . json_encode($context));
        error_log("DTF Gang Builder: Failed to log to database - " . $e->getMessage());
    }
}

/**
 * Check if database is installed/working
 */
function dtf_is_database_installed() {
    try {
        $db = dtf_db();
        return $db->tableExists('users');
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Install database tables
 */
function dtf_install_database() {
    try {
        $db = dtf_db();
        return $db->createTables();
    } catch (Exception $e) {
        dtf_log('ERROR', 'Database installation failed', ['error' => $e->getMessage()]);
        return false;
    }
}

// Auto-install database if not exists
if (!dtf_is_database_installed()) {
    try {
        dtf_install_database();
        dtf_log('INFO', 'Database initialized successfully');
    } catch (Exception $e) {
        error_log("DTF Gang Builder: Failed to initialize database - " . $e->getMessage());
    }
}
