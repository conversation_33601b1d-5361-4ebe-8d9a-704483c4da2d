<?php
/**
 * DTF Gang Builder - Database Configuration and Connection
 * 
 * This file handles database connections, queries, and database-related operations
 * for the DTF Gang Builder application.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

// Include configuration
require_once DTF_INCLUDES_PATH . 'config.php';

/**
 * Database Connection Class
 */
class DTF_Database {
    private static $instance = null;
    private $connection = null;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;
    private $options;

    /**
     * Constructor
     */
    private function __construct() {
        $this->options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
    }

    /**
     * Get database instance (Singleton pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get database connection
     */
    public function getConnection() {
        if ($this->connection === null) {
            try {
                // Ensure data directory exists
                $data_dir = dirname(DTF_DB_PATH);
                if (!is_dir($data_dir)) {
                    mkdir($data_dir, 0755, true);
                }

                $dsn = "sqlite:" . DTF_DB_PATH;
                $this->connection = new PDO($dsn, null, null, $this->options);

                // Enable foreign keys for SQLite
                $this->connection->exec('PRAGMA foreign_keys = ON');

                // Log successful connection
                if (DTF_DEBUG) {
                    error_log("DTF Gang Builder: SQLite database connection successful");
                }
            } catch (PDOException $e) {
                error_log("DTF Gang Builder Database Error: " . $e->getMessage());
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
        return $this->connection;
    }

    /**
     * Execute a query
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("DTF Gang Builder Query Error: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Query execution failed: " . $e->getMessage());
        }
    }

    /**
     * Fetch single row
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insert data and return last insert ID
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO " . DTF_DB_PREFIX . "{$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->getConnection()->lastInsertId();
    }

    /**
     * Update data
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE " . DTF_DB_PREFIX . "{$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Delete data
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM " . DTF_DB_PREFIX . "{$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Check if table exists
     */
    public function tableExists($table) {
        $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name = :table";
        $stmt = $this->query($sql, ['table' => DTF_DB_PREFIX . $table]);
        return $stmt->rowCount() > 0;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->getConnection()->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
}

/**
 * Database Installation and Migration Functions
 */
class DTF_DatabaseInstaller {
    private $db;

    public function __construct() {
        $this->db = DTF_Database::getInstance();
    }

    /**
     * Install database tables
     */
    public function install() {
        try {
            $this->createUsersTable();
            $this->createProjectsTable();
            $this->createImagesTable();
            $this->createGangSheetsTable();
            $this->createOrdersTable();
            $this->createSessionsTable();
            $this->createLogsTable();
            
            return true;
        } catch (Exception $e) {
            error_log("DTF Gang Builder Installation Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create users table
     */
    private function createUsersTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            email TEXT NULL,
            name TEXT NULL,
            ip_address TEXT NOT NULL,
            user_agent TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_users_session_id ON " . DTF_DB_PREFIX . "users(session_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_users_email ON " . DTF_DB_PREFIX . "users(email)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_users_created_at ON " . DTF_DB_PREFIX . "users(created_at)");
    }

    /**
     * Create projects table
     */
    private function createProjectsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            project_uuid TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            description TEXT NULL,
            sheet_size TEXT NOT NULL DEFAULT '30x72',
            configuration TEXT NULL,
            status TEXT DEFAULT 'draft',
            share_token TEXT NULL,
            share_expires_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_user_id ON " . DTF_DB_PREFIX . "projects(user_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_project_uuid ON " . DTF_DB_PREFIX . "projects(project_uuid)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_share_token ON " . DTF_DB_PREFIX . "projects(share_token)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_status ON " . DTF_DB_PREFIX . "projects(status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_projects_created_at ON " . DTF_DB_PREFIX . "projects(created_at)");
    }

    /**
     * Create images table
     */
    private function createImagesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            filename TEXT NOT NULL,
            original_filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            mime_type TEXT NOT NULL,
            width INTEGER NOT NULL,
            height INTEGER NOT NULL,
            dpi INTEGER DEFAULT 300,
            thumbnail_path TEXT NULL,
            position_x REAL DEFAULT 0,
            position_y REAL DEFAULT 0,
            scale_x REAL DEFAULT 1,
            scale_y REAL DEFAULT 1,
            rotation REAL DEFAULT 0,
            z_index INTEGER DEFAULT 0,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_project_id ON " . DTF_DB_PREFIX . "images(project_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_filename ON " . DTF_DB_PREFIX . "images(filename)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_is_active ON " . DTF_DB_PREFIX . "images(is_active)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_images_created_at ON " . DTF_DB_PREFIX . "images(created_at)");
    }

    /**
     * Create gang sheets table
     */
    private function createGangSheetsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "gang_sheets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            sheet_name TEXT NOT NULL,
            width REAL NOT NULL,
            height REAL NOT NULL,
            dpi INTEGER DEFAULT 300,
            bleed REAL DEFAULT 0.125,
            margin REAL DEFAULT 0.25,
            background_color TEXT DEFAULT '#FFFFFF',
            grid_enabled INTEGER DEFAULT 1,
            grid_spacing REAL DEFAULT 1,
            configuration TEXT NULL,
            pdf_path TEXT NULL,
            preview_path TEXT NULL,
            status TEXT DEFAULT 'draft',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_gang_sheets_project_id ON " . DTF_DB_PREFIX . "gang_sheets(project_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_gang_sheets_status ON " . DTF_DB_PREFIX . "gang_sheets(status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_gang_sheets_created_at ON " . DTF_DB_PREFIX . "gang_sheets(created_at)");
    }

    /**
     * Create orders table
     */
    private function createOrdersTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            order_number TEXT NOT NULL UNIQUE,
            customer_email TEXT NOT NULL,
            customer_name TEXT NULL,
            customer_phone TEXT NULL,
            billing_address TEXT NULL,
            shipping_address TEXT NULL,
            quantity INTEGER DEFAULT 1,
            unit_price REAL DEFAULT 0.00,
            total_price REAL DEFAULT 0.00,
            tax_amount REAL DEFAULT 0.00,
            shipping_cost REAL DEFAULT 0.00,
            payment_status TEXT DEFAULT 'pending',
            payment_method TEXT NULL,
            payment_transaction_id TEXT NULL,
            order_status TEXT DEFAULT 'pending',
            notes TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_orders_project_id ON " . DTF_DB_PREFIX . "orders(project_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_orders_order_number ON " . DTF_DB_PREFIX . "orders(order_number)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON " . DTF_DB_PREFIX . "orders(customer_email)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON " . DTF_DB_PREFIX . "orders(payment_status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_orders_order_status ON " . DTF_DB_PREFIX . "orders(order_status)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_orders_created_at ON " . DTF_DB_PREFIX . "orders(created_at)");
    }

    /**
     * Create sessions table
     */
    private function createSessionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "sessions (
            id TEXT PRIMARY KEY,
            user_id INTEGER NULL,
            ip_address TEXT NOT NULL,
            user_agent TEXT NULL,
            payload TEXT NOT NULL,
            last_activity INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE SET NULL
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON " . DTF_DB_PREFIX . "sessions(user_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_sessions_last_activity ON " . DTF_DB_PREFIX . "sessions(last_activity)");
    }

    /**
     * Create logs table
     */
    private function createLogsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            level TEXT NOT NULL,
            message TEXT NOT NULL,
            context TEXT NULL,
            user_id INTEGER NULL,
            ip_address TEXT NULL,
            user_agent TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE SET NULL
        )";

        $this->db->query($sql);

        // Create indexes
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_logs_level ON " . DTF_DB_PREFIX . "logs(level)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_logs_user_id ON " . DTF_DB_PREFIX . "logs(user_id)");
        $this->db->query("CREATE INDEX IF NOT EXISTS idx_logs_created_at ON " . DTF_DB_PREFIX . "logs(created_at)");
    }
}

/**
 * Helper functions for database operations
 */

/**
 * Get database instance
 */
function dtf_db() {
    return DTF_Database::getInstance();
}

/**
 * Install database tables
 */
function dtf_install_database() {
    $installer = new DTF_DatabaseInstaller();
    return $installer->install();
}

/**
 * Check if database is installed
 */
function dtf_is_database_installed() {
    try {
        $db = DTF_Database::getInstance();
        return $db->tableExists('users') && 
               $db->tableExists('projects') && 
               $db->tableExists('images') && 
               $db->tableExists('gang_sheets') && 
               $db->tableExists('orders');
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get or create user by session
 */
function dtf_get_or_create_user() {
    $db = dtf_db();
    $session_id = session_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Try to find existing user by session
    $user = $db->fetch(
        "SELECT * FROM " . DTF_DB_PREFIX . "users WHERE session_id = :session_id",
        ['session_id' => $session_id]
    );

    if (!$user) {
        // Create new user
        $user_id = $db->insert('users', [
            'session_id' => $session_id,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent
        ]);

        $user = $db->fetch(
            "SELECT * FROM " . DTF_DB_PREFIX . "users WHERE id = :id",
            ['id' => $user_id]
        );
    } else {
        // Update last activity
        $db->update('users', 
            ['last_activity' => date('Y-m-d H:i:s')],
            'id = :id',
            ['id' => $user['id']]
        );
    }

    return $user;
}

// Auto-install database if not exists
if (!dtf_is_database_installed()) {
    try {
        dtf_install_database();
        if (DTF_DEBUG) {
            error_log("DTF Gang Builder: Database tables created successfully");
        }
    } catch (Exception $e) {
        error_log("DTF Gang Builder: Failed to create database tables - " . $e->getMessage());
    }
}

?>
