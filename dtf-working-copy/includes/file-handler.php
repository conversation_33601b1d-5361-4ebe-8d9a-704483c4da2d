<?php
/**
 * DTF Gang Builder - Enhanced File Handler
 * 
 * Comprehensive file handling system supporting multiple formats,
 * batch processing, validation, and optimization.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * Enhanced File Handler Class
 */
class DTF_FileHandler {
    
    // Supported file types with their MIME types and extensions
    const SUPPORTED_FORMATS = [
        'image/jpeg' => ['jpg', 'jpeg'],
        'image/png' => ['png'],
        'image/gif' => ['gif'],
        'image/webp' => ['webp'],
        'image/svg+xml' => ['svg'],
        'application/pdf' => ['pdf'],
        'application/postscript' => ['eps', 'ai'],
        'image/tiff' => ['tiff', 'tif'],
        'image/bmp' => ['bmp']
    ];
    
    // File size limits (in bytes)
    const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    const MAX_BATCH_SIZE = 50; // Maximum files per batch
    const MAX_TOTAL_BATCH_SIZE = 500 * 1024 * 1024; // 500MB total
    
    // Image processing settings
    const DEFAULT_MAX_WIDTH = 4000;
    const DEFAULT_MAX_HEIGHT = 4000;
    const DEFAULT_QUALITY = 90;
    const DEFAULT_DPI = 300;
    
    private $upload_path;
    private $temp_path;
    private $processed_path;
    
    public function __construct() {
        $this->upload_path = DTF_UPLOADS_PATH;
        $this->temp_path = DTF_TEMP_PATH;
        $this->processed_path = DTF_UPLOADS_PATH . 'processed/';
        
        $this->ensureDirectories();
    }
    
    /**
     * Process uploaded files (single or batch)
     */
    public function processUpload($files) {
        try {
            // Normalize files array for consistent processing
            $files = $this->normalizeFilesArray($files);
            
            // Validate batch
            $this->validateBatch($files);
            
            $results = [];
            $total_processed = 0;
            
            foreach ($files as $file) {
                try {
                    $result = $this->processSingleFile($file);
                    if ($result) {
                        $results[] = $result;
                        $total_processed++;
                    }
                } catch (Exception $e) {
                    dtf_log('WARNING', 'File processing failed', [
                        'file' => $file['name'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                    
                    $results[] = [
                        'success' => false,
                        'filename' => $file['name'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            dtf_log('INFO', 'Batch file processing completed', [
                'total_files' => count($files),
                'processed' => $total_processed,
                'failed' => count($files) - $total_processed
            ]);
            
            return [
                'success' => true,
                'total_files' => count($files),
                'processed' => $total_processed,
                'failed' => count($files) - $total_processed,
                'results' => $results
            ];
            
        } catch (Exception $e) {
            dtf_log('ERROR', 'Batch processing failed', [
                'error' => $e->getMessage(),
                'files_count' => count($files)
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process a single uploaded file
     */
    private function processSingleFile($file) {
        // Validate file
        $this->validateFile($file);
        
        // Generate unique filename
        $file_info = $this->generateFileInfo($file);
        
        // Move uploaded file to temp location
        $temp_path = $this->temp_path . $file_info['temp_name'];
        if (!move_uploaded_file($file['tmp_name'], $temp_path)) {
            throw new Exception('Failed to move uploaded file');
        }
        
        // Process based on file type
        $processed_file = $this->processFileByType($temp_path, $file_info);
        
        // Generate thumbnail
        $thumbnail = $this->generateThumbnail($processed_file['path'], $file_info);
        
        // Clean up temp file
        if (file_exists($temp_path)) {
            unlink($temp_path);
        }
        
        return [
            'success' => true,
            'id' => $file_info['id'],
            'filename' => $file_info['filename'],
            'original_name' => $file['name'],
            'path' => $processed_file['path'],
            'url' => $processed_file['url'],
            'thumbnail' => $thumbnail,
            'width' => $processed_file['width'],
            'height' => $processed_file['height'],
            'size' => $processed_file['size'],
            'format' => $file_info['format'],
            'mime_type' => $file_info['mime_type'],
            'dpi' => $processed_file['dpi'] ?? self::DEFAULT_DPI,
            'color_space' => $processed_file['color_space'] ?? 'RGB',
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Process file based on its type
     */
    private function processFileByType($temp_path, $file_info) {
        $final_path = $this->processed_path . $file_info['filename'];
        $final_url = str_replace(DTF_BASE_PATH, DTF_BASE_URL, $final_path);
        
        switch ($file_info['format']) {
            case 'svg':
                return $this->processSVG($temp_path, $final_path, $final_url);
                
            case 'pdf':
                return $this->processPDF($temp_path, $final_path, $final_url);
                
            case 'eps':
            case 'ai':
                return $this->processEPS($temp_path, $final_path, $final_url);
                
            default:
                return $this->processRasterImage($temp_path, $final_path, $final_url);
        }
    }
    
    /**
     * Process SVG files
     */
    private function processSVG($temp_path, $final_path, $final_url) {
        // For SVG, we can copy directly but should validate and sanitize
        $svg_content = file_get_contents($temp_path);
        
        // Basic SVG validation
        if (strpos($svg_content, '<svg') === false) {
            throw new Exception('Invalid SVG file');
        }
        
        // Sanitize SVG (remove scripts, etc.)
        $svg_content = $this->sanitizeSVG($svg_content);
        
        // Save sanitized SVG
        if (!file_put_contents($final_path, $svg_content)) {
            throw new Exception('Failed to save SVG file');
        }
        
        // Extract dimensions from SVG
        $dimensions = $this->extractSVGDimensions($svg_content);
        
        return [
            'path' => $final_path,
            'url' => $final_url,
            'width' => $dimensions['width'],
            'height' => $dimensions['height'],
            'size' => filesize($final_path),
            'dpi' => 300, // SVG is vector, but we'll assume 300 DPI for calculations
            'color_space' => 'RGB'
        ];
    }
    
    /**
     * Process PDF files
     */
    private function processPDF($temp_path, $final_path, $final_url) {
        // For now, copy PDF directly
        // In production, you might want to convert first page to image
        if (!copy($temp_path, $final_path)) {
            throw new Exception('Failed to save PDF file');
        }
        
        // Extract PDF info (would need PDF library for full implementation)
        $size = filesize($final_path);
        
        return [
            'path' => $final_path,
            'url' => $final_url,
            'width' => 2550, // Assume 8.5" at 300 DPI
            'height' => 3300, // Assume 11" at 300 DPI
            'size' => $size,
            'dpi' => 300,
            'color_space' => 'CMYK'
        ];
    }
    
    /**
     * Process EPS/AI files
     */
    private function processEPS($temp_path, $final_path, $final_url) {
        // For now, copy directly
        // In production, you might want to convert to raster
        if (!copy($temp_path, $final_path)) {
            throw new Exception('Failed to save EPS/AI file');
        }
        
        $size = filesize($final_path);
        
        return [
            'path' => $final_path,
            'url' => $final_url,
            'width' => 2550, // Default dimensions
            'height' => 3300,
            'size' => $size,
            'dpi' => 300,
            'color_space' => 'CMYK'
        ];
    }
    
    /**
     * Process raster images (PNG, JPG, etc.)
     */
    private function processRasterImage($temp_path, $final_path, $final_url) {
        // Get image info
        $image_info = getimagesize($temp_path);
        if (!$image_info) {
            throw new Exception('Invalid image file');
        }
        
        $width = $image_info[0];
        $height = $image_info[1];
        $mime_type = $image_info['mime'];
        
        // Create image resource
        $image = $this->createImageResource($temp_path, $mime_type);
        if (!$image) {
            throw new Exception('Failed to create image resource');
        }
        
        // Optimize image if needed
        $optimized = $this->optimizeImage($image, $width, $height);
        
        // Save optimized image
        $saved = $this->saveOptimizedImage($optimized['image'], $final_path, $optimized['format']);
        if (!$saved) {
            imagedestroy($optimized['image']);
            throw new Exception('Failed to save optimized image');
        }
        
        // Clean up
        imagedestroy($image);
        if ($optimized['image'] !== $image) {
            imagedestroy($optimized['image']);
        }
        
        return [
            'path' => $final_path,
            'url' => $final_url,
            'width' => $optimized['width'],
            'height' => $optimized['height'],
            'size' => filesize($final_path),
            'dpi' => self::DEFAULT_DPI,
            'color_space' => 'RGB'
        ];
    }
    
    /**
     * Generate thumbnail for uploaded file
     */
    private function generateThumbnail($file_path, $file_info) {
        $thumb_dir = $this->processed_path . 'thumbnails/';
        if (!is_dir($thumb_dir)) {
            mkdir($thumb_dir, 0755, true);
        }
        
        $thumb_filename = 'thumb_' . $file_info['filename'];
        $thumb_path = $thumb_dir . $thumb_filename;
        $thumb_url = str_replace(DTF_BASE_PATH, DTF_BASE_URL, $thumb_path);
        
        // For raster images, create actual thumbnail
        if (in_array($file_info['format'], ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            $this->createImageThumbnail($file_path, $thumb_path, 150, 150);
        } else {
            // For vector files, use a placeholder or convert first
            $this->createPlaceholderThumbnail($thumb_path, $file_info['format']);
        }
        
        return [
            'path' => $thumb_path,
            'url' => $thumb_url
        ];
    }
    
    // Additional helper methods would continue here...
    // (validateFile, normalizeFilesArray, generateFileInfo, etc.)
    
    /**
     * Ensure required directories exist
     */
    private function ensureDirectories() {
        $dirs = [
            $this->upload_path,
            $this->temp_path,
            $this->processed_path,
            $this->processed_path . 'thumbnails/'
        ];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
}

// Helper functions for file handling
function dtf_process_upload($files) {
    $handler = new DTF_FileHandler();
    return $handler->processUpload($files);
}

function dtf_get_supported_formats() {
    return DTF_FileHandler::SUPPORTED_FORMATS;
}

function dtf_validate_file_type($mime_type) {
    return array_key_exists($mime_type, DTF_FileHandler::SUPPORTED_FORMATS);
}

?>
