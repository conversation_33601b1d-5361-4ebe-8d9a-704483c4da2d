<?php
/**
 * DTF Gang Builder - Core Functions
 * 
 * This file contains core utility functions used throughout the DTF Gang Builder application.
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

// Required files are now included in the main application files

/**
 * =============================================================================
 * FILE HANDLING FUNCTIONS
 * =============================================================================
 */

/**
 * Validate uploaded file
 */
function dtf_validate_file($file) {
    $errors = [];

    // Check if file was uploaded
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        $errors[] = 'No file was uploaded';
        return $errors;
    }

    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        switch ($file['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errors[] = 'File is too large';
                break;
            case UPLOAD_ERR_PARTIAL:
                $errors[] = 'File upload was interrupted';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errors[] = 'Temporary directory not found';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errors[] = 'Failed to write file to disk';
                break;
            default:
                $errors[] = 'Unknown upload error';
        }
        return $errors;
    }

    // Check file size
    if ($file['size'] > DTF_MAX_FILE_SIZE) {
        $errors[] = 'File size exceeds maximum allowed size of ' . dtf_format_bytes(DTF_MAX_FILE_SIZE);
    }

    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, DTF_ALLOWED_EXTENSIONS)) {
        $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', DTF_ALLOWED_EXTENSIONS);
    }

    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mime_type, DTF_ALLOWED_MIME_TYPES)) {
        $errors[] = 'Invalid file type detected';
    }

    // Check if it's actually an image
    $image_info = getimagesize($file['tmp_name']);
    if ($image_info === false) {
        $errors[] = 'File is not a valid image';
    } else {
        // Check image dimensions
        $width = $image_info[0];
        $height = $image_info[1];

        if ($width < DTF_MIN_IMAGE_WIDTH || $height < DTF_MIN_IMAGE_HEIGHT) {
            $errors[] = "Image dimensions too small. Minimum: " . DTF_MIN_IMAGE_WIDTH . "x" . DTF_MIN_IMAGE_HEIGHT;
        }

        if ($width > DTF_MAX_IMAGE_WIDTH || $height > DTF_MAX_IMAGE_HEIGHT) {
            $errors[] = "Image dimensions too large. Maximum: " . DTF_MAX_IMAGE_WIDTH . "x" . DTF_MAX_IMAGE_HEIGHT;
        }
    }

    return $errors;
}

/**
 * Generate unique filename
 */
function dtf_generate_filename($original_filename, $prefix = '') {
    $extension = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
    $basename = pathinfo($original_filename, PATHINFO_FILENAME);
    $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
    $basename = substr($basename, 0, 50); // Limit length
    
    $unique_id = uniqid($prefix, true);
    $timestamp = date('Ymd_His');
    
    return $timestamp . '_' . $unique_id . '_' . $basename . '.' . $extension;
}

/**
 * Create thumbnail
 */
function dtf_create_thumbnail($source_path, $thumbnail_path, $width = null, $height = null) {
    $width = $width ?: DTF_THUMBNAIL_WIDTH;
    $height = $height ?: DTF_THUMBNAIL_HEIGHT;

    $image_info = getimagesize($source_path);
    if ($image_info === false) {
        return false;
    }

    $source_width = $image_info[0];
    $source_height = $image_info[1];
    $source_type = $image_info[2];

    // Calculate thumbnail dimensions maintaining aspect ratio
    $aspect_ratio = $source_width / $source_height;
    if ($width / $height > $aspect_ratio) {
        $width = $height * $aspect_ratio;
    } else {
        $height = $width / $aspect_ratio;
    }

    // Create source image
    switch ($source_type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source_path);
            break;
        case IMAGETYPE_WEBP:
            $source_image = imagecreatefromwebp($source_path);
            break;
        default:
            return false;
    }

    if (!$source_image) {
        return false;
    }

    // Create thumbnail
    $thumbnail = imagecreatetruecolor($width, $height);
    
    // Preserve transparency for PNG and GIF
    if ($source_type == IMAGETYPE_PNG || $source_type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $width, $height, $transparent);
    }

    // Resize image
    imagecopyresampled($thumbnail, $source_image, 0, 0, 0, 0, $width, $height, $source_width, $source_height);

    // Save thumbnail
    $result = false;
    switch ($source_type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumbnail, $thumbnail_path, DTF_THUMBNAIL_QUALITY);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($thumbnail, $thumbnail_path, 6);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumbnail, $thumbnail_path);
            break;
        case IMAGETYPE_WEBP:
            $result = imagewebp($thumbnail, $thumbnail_path, DTF_THUMBNAIL_QUALITY);
            break;
    }

    // Clean up
    imagedestroy($source_image);
    imagedestroy($thumbnail);

    return $result;
}

/**
 * =============================================================================
 * UTILITY FUNCTIONS
 * =============================================================================
 */

/**
 * Format bytes to human readable format
 */
function dtf_format_bytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Generate UUID v4
 */
function dtf_generate_uuid() {
    $data = random_bytes(16);
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // Set version to 0100
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // Set bits 6-7 to 10
    
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

/**
 * Generate random token
 */
function dtf_generate_token($length = 32) {
    return bin2hex(random_bytes($length));
}

// dtf_log function is now defined in database-abstraction.php

/**
 * Send JSON response
 */
function dtf_json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Send error response
 */
function dtf_error_response($message, $status_code = 400, $details = []) {
    dtf_json_response([
        'success' => false,
        'error' => $message,
        'details' => $details
    ], $status_code);
}

/**
 * Send success response
 */
function dtf_success_response($data = [], $message = 'Success') {
    dtf_json_response([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

/**
 * =============================================================================
 * VALIDATION FUNCTIONS
 * =============================================================================
 */

/**
 * Validate email
 */
function dtf_validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate sheet size
 */
function dtf_validate_sheet_size($size) {
    $sheet_sizes = unserialize(DTF_SHEET_SIZES);
    return array_key_exists($size, $sheet_sizes);
}

/**
 * Validate numeric value
 */
function dtf_validate_numeric($value, $min = null, $max = null) {
    if (!is_numeric($value)) {
        return false;
    }
    
    $value = floatval($value);
    
    if ($min !== null && $value < $min) {
        return false;
    }
    
    if ($max !== null && $value > $max) {
        return false;
    }
    
    return true;
}

/**
 * =============================================================================
 * SHEET CALCULATION FUNCTIONS
 * =============================================================================
 */

/**
 * Convert inches to pixels at given DPI
 */
function dtf_inches_to_pixels($inches, $dpi = null) {
    $dpi = $dpi ?: DTF_DEFAULT_DPI;
    return round($inches * $dpi);
}

/**
 * Convert pixels to inches at given DPI
 */
function dtf_pixels_to_inches($pixels, $dpi = null) {
    $dpi = $dpi ?: DTF_DEFAULT_DPI;
    return $pixels / $dpi;
}

/**
 * Get sheet dimensions in pixels
 */
function dtf_get_sheet_dimensions($sheet_size, $dpi = null) {
    $dpi = $dpi ?: DTF_SHEET_DPI;
    $sheet_sizes = unserialize(DTF_SHEET_SIZES);
    
    if (!isset($sheet_sizes[$sheet_size])) {
        return false;
    }
    
    $size = $sheet_sizes[$sheet_size];
    
    return [
        'width_inches' => $size['width'],
        'height_inches' => $size['height'],
        'width_pixels' => dtf_inches_to_pixels($size['width'], $dpi),
        'height_pixels' => dtf_inches_to_pixels($size['height'], $dpi),
        'dpi' => $dpi
    ];
}

/**
 * Calculate optimal image arrangement
 */
function dtf_calculate_optimal_arrangement($images, $sheet_dimensions) {
    // This is a simplified arrangement algorithm
    // In a production environment, you might want to implement a more sophisticated
    // bin packing algorithm for optimal space utilization
    
    $arrangements = [];
    $current_x = DTF_SHEET_MARGIN;
    $current_y = DTF_SHEET_MARGIN;
    $row_height = 0;
    
    foreach ($images as $image) {
        $image_width = $image['width'] / DTF_DEFAULT_DPI; // Convert to inches
        $image_height = $image['height'] / DTF_DEFAULT_DPI;
        
        // Check if image fits in current row
        if ($current_x + $image_width + DTF_SHEET_MARGIN > $sheet_dimensions['width_inches']) {
            // Move to next row
            $current_x = DTF_SHEET_MARGIN;
            $current_y += $row_height + DTF_SHEET_MARGIN;
            $row_height = 0;
        }
        
        // Check if image fits in sheet
        if ($current_y + $image_height + DTF_SHEET_MARGIN <= $sheet_dimensions['height_inches']) {
            $arrangements[] = [
                'image_id' => $image['id'],
                'x' => $current_x,
                'y' => $current_y,
                'width' => $image_width,
                'height' => $image_height
            ];
            
            $current_x += $image_width + DTF_SHEET_MARGIN;
            $row_height = max($row_height, $image_height);
        }
    }
    
    return $arrangements;
}

/**
 * =============================================================================
 * INITIALIZATION
 * =============================================================================
 */

// Create necessary directories if they don't exist
$temp_dirs = [
    DTF_TEMP_PATH . 'processing/',
    DTF_TEMP_PATH . 'thumbnails/',
    DTF_TEMP_PATH . 'cache/',
    DTF_TEMP_PATH . 'quarantine/'
];

foreach ($temp_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Set up error logging
if (!is_dir(DTF_LOG_PATH)) {
    mkdir(DTF_LOG_PATH, 0755, true);
}

// Log application start
dtf_log('INFO', 'DTF Gang Builder functions loaded', [
    'version' => DTF_APP_VERSION,
    'environment' => DTF_ENVIRONMENT
]);

?>
