<?php
/**
 * DTF Gang Builder - JSON Database
 * Simple file-based storage system that mimics database functionality
 */

// Prevent direct access
if (!defined('DTF_GANG_BUILDER')) {
    die('Direct access not permitted');
}

/**
 * JSON Database Class
 * Provides database-like functionality using JSON files
 */
class DTF_JsonDatabase {
    private static $instance = null;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->initializeFiles();
    }
    
    /**
     * Initialize JSON files
     */
    private function initializeFiles() {
        $files = [
            DTF_USERS_FILE => [],
            DTF_PROJECTS_FILE => [],
            DTF_IMAGES_FILE => [],
            DTF_LOGS_FILE => []
        ];
        
        foreach ($files as $file => $default_data) {
            if (!file_exists($file)) {
                $this->writeJsonFile($file, $default_data);
            }
        }
    }
    
    /**
     * Read JSON file
     */
    private function readJsonFile($file) {
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file_get_contents($file);
        $data = json_decode($content, true);
        
        return $data ?: [];
    }
    
    /**
     * Write JSON file
     */
    private function writeJsonFile($file, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file, $json, LOCK_EX) !== false;
    }
    
    /**
     * Generate unique ID
     */
    private function generateId($table) {
        $data = $this->readJsonFile($this->getTableFile($table));
        $max_id = 0;
        
        foreach ($data as $record) {
            if (isset($record['id']) && $record['id'] > $max_id) {
                $max_id = $record['id'];
            }
        }
        
        return $max_id + 1;
    }
    
    /**
     * Get table file path
     */
    private function getTableFile($table) {
        switch ($table) {
            case 'users':
                return DTF_USERS_FILE;
            case 'projects':
                return DTF_PROJECTS_FILE;
            case 'images':
                return DTF_IMAGES_FILE;
            case 'logs':
                return DTF_LOGS_FILE;
            default:
                throw new Exception("Unknown table: $table");
        }
    }
    
    /**
     * Insert record
     */
    public function insert($table, $data) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        
        $data['id'] = $this->generateId($table);
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $records[] = $data;
        
        if ($this->writeJsonFile($file, $records)) {
            return $data['id'];
        }
        
        throw new Exception("Failed to insert record into $table");
    }
    
    /**
     * Find record by criteria
     */
    public function fetch($table, $where = [], $limit = 1) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $results = [];
        
        foreach ($records as $record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                $results[] = $record;
                if (count($results) >= $limit) {
                    break;
                }
            }
        }
        
        return $limit === 1 ? ($results[0] ?? null) : $results;
    }
    
    /**
     * Find all records by criteria
     */
    public function fetchAll($table, $where = [], $order_by = null, $limit = null) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $results = [];
        
        foreach ($records as $record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                $results[] = $record;
            }
        }
        
        // Simple ordering
        if ($order_by) {
            usort($results, function($a, $b) use ($order_by) {
                if (strpos($order_by, 'DESC') !== false) {
                    $field = str_replace(' DESC', '', $order_by);
                    return ($b[$field] ?? '') <=> ($a[$field] ?? '');
                } else {
                    $field = str_replace(' ASC', '', $order_by);
                    return ($a[$field] ?? '') <=> ($b[$field] ?? '');
                }
            });
        }
        
        // Apply limit
        if ($limit) {
            $results = array_slice($results, 0, $limit);
        }
        
        return $results;
    }
    
    /**
     * Update record
     */
    public function update($table, $data, $where) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $updated = 0;
        
        foreach ($records as &$record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                foreach ($data as $field => $value) {
                    $record[$field] = $value;
                }
                $record['updated_at'] = date('Y-m-d H:i:s');
                $updated++;
            }
        }
        
        if ($updated > 0) {
            $this->writeJsonFile($file, $records);
        }
        
        return $updated;
    }
    
    /**
     * Delete record
     */
    public function delete($table, $where) {
        $file = $this->getTableFile($table);
        $records = $this->readJsonFile($file);
        $new_records = [];
        $deleted = 0;
        
        foreach ($records as $record) {
            $match = true;
            
            foreach ($where as $field => $value) {
                if (!isset($record[$field]) || $record[$field] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if (!$match) {
                $new_records[] = $record;
            } else {
                $deleted++;
            }
        }
        
        if ($deleted > 0) {
            $this->writeJsonFile($file, $new_records);
        }
        
        return $deleted;
    }
    
    /**
     * Check if table exists (always true for JSON storage)
     */
    public function tableExists($table) {
        try {
            $this->getTableFile($table);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

/**
 * Helper functions for JSON database operations
 */

/**
 * Get database instance
 */
function dtf_db() {
    return DTF_JsonDatabase::getInstance();
}

/**
 * Install database tables (create JSON files)
 */
function dtf_install_database() {
    try {
        DTF_JsonDatabase::getInstance();
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Check if database is installed
 */
function dtf_is_database_installed() {
    return file_exists(DTF_USERS_FILE) && 
           file_exists(DTF_PROJECTS_FILE) && 
           file_exists(DTF_IMAGES_FILE) && 
           file_exists(DTF_LOGS_FILE);
}

/**
 * Get or create user by session
 */
function dtf_get_or_create_user() {
    $db = dtf_db();
    $session_id = session_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Try to find existing user by session
    $user = $db->fetch('users', ['session_id' => $session_id]);

    if (!$user) {
        // Create new user
        $user_id = $db->insert('users', [
            'session_id' => $session_id,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent,
            'email' => null,
            'name' => null,
            'last_activity' => date('Y-m-d H:i:s')
        ]);

        $user = $db->fetch('users', ['id' => $user_id]);
    } else {
        // Update last activity
        $db->update('users', 
            ['last_activity' => date('Y-m-d H:i:s')],
            ['id' => $user['id']]
        );
    }

    return $user;
}

// Auto-install database if not exists
if (!dtf_is_database_installed()) {
    try {
        dtf_install_database();
        if (DTF_DEBUG) {
            error_log("DTF Gang Builder: JSON database files created successfully");
        }
    } catch (Exception $e) {
        error_log("DTF Gang Builder: Failed to create JSON database files - " . $e->getMessage());
    }
}

?>
