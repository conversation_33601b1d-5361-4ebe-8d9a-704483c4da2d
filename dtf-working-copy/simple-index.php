<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Sheet Builder</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .canvas-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 600px;
        }

        /* Upload Area */
        .upload-zone {
            border: 3px dashed #ccc;
            border-radius: 10px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .upload-zone:hover,
        .upload-zone.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 15px;
        }

        .upload-zone:hover .upload-icon {
            color: #667eea;
        }

        .file-input {
            display: none;
        }

        /* Sheet Size */
        .sheet-controls {
            margin-bottom: 20px;
        }

        .sheet-controls h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .size-selector {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        /* Quantity Controls */
        .quantity-controls {
            margin-bottom: 20px;
        }

        .quantity-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            text-align: center;
        }

        /* Buttons */
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        /* Canvas */
        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            position: relative;
            overflow: hidden;
        }

        #gang-sheet-canvas {
            display: block;
            margin: 0 auto;
            background: white;
        }

        /* Preview */
        .image-preview {
            margin-top: 20px;
        }

        .preview-image {
            max-width: 100%;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        /* Status */
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DTF Gang Sheet Builder</h1>
            <p>Upload your image and we'll create a gang sheet with multiple copies</p>
        </div>

        <div class="main-grid">
            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Upload -->
                <div class="upload-section">
                    <h3>1. Upload Your Image</h3>
                    <div id="upload-zone" class="upload-zone">
                        <div class="upload-icon">📁</div>
                        <div>Click or drag image here</div>
                        <small>PNG, JPG, JPEG, GIF (Max 10MB)</small>
                    </div>
                    <input type="file" id="file-input" class="file-input" accept="image/*">
                </div>

                <!-- Sheet Size -->
                <div class="sheet-controls">
                    <h3>2. Choose Sheet Size</h3>
                    <select id="sheet-size" class="size-selector">
                        <option value="30x12">30" × 12"</option>
                        <option value="30x24">30" × 24"</option>
                        <option value="30x36">30" × 36"</option>
                        <option value="30x48">30" × 48"</option>
                        <option value="30x60">30" × 60"</option>
                        <option value="30x72" selected>30" × 72"</option>
                        <option value="30x100">30" × 100"</option>
                        <option value="30x120">30" × 120"</option>
                    </select>
                </div>

                <!-- Quantity -->
                <div class="quantity-controls">
                    <h3>3. Copies Per Sheet</h3>
                    <input type="number" id="quantity" class="quantity-input" value="12" min="1" max="100">
                    <small>How many copies to fit on the sheet</small>
                </div>

                <!-- Actions -->
                <div class="actions">
                    <button id="generate-btn" class="btn btn-primary" disabled>
                        Generate Gang Sheet
                    </button>
                    <button id="download-btn" class="btn btn-success hidden">
                        Download PDF
                    </button>
                </div>

                <!-- Image Preview -->
                <div id="image-preview" class="image-preview hidden">
                    <h4>Your Image:</h4>
                    <img id="preview-img" class="preview-image" alt="Preview">
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <h3>Gang Sheet Preview</h3>
                <div class="canvas-container">
                    <canvas id="gang-sheet-canvas" width="800" height="600"></canvas>
                </div>
                
                <div id="status" class="status hidden"></div>
            </div>
        </div>
    </div>

    <script>
        class SimpleGangBuilder {
            constructor() {
                this.canvas = null;
                this.ctx = null;
                this.uploadedImage = null;
                this.sheetSize = '30x72';
                this.quantity = 12;
                
                this.init();
            }

            init() {
                this.canvas = document.getElementById('gang-sheet-canvas');
                this.ctx = this.canvas.getContext('2d');
                
                this.setupEventListeners();
                this.drawEmptySheet();
            }

            setupEventListeners() {
                // Upload
                const uploadZone = document.getElementById('upload-zone');
                const fileInput = document.getElementById('file-input');
                
                uploadZone.addEventListener('click', () => fileInput.click());
                uploadZone.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadZone.addEventListener('drop', this.handleDrop.bind(this));
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // Controls
                document.getElementById('sheet-size').addEventListener('change', (e) => {
                    this.sheetSize = e.target.value;
                    this.updateCanvas();
                });

                document.getElementById('quantity').addEventListener('input', (e) => {
                    this.quantity = parseInt(e.target.value) || 1;
                    this.updateCanvas();
                });

                // Generate
                document.getElementById('generate-btn').addEventListener('click', () => {
                    this.generateGangSheet();
                });

                // Download
                document.getElementById('download-btn').addEventListener('click', () => {
                    this.downloadPDF();
                });
            }

            handleDragOver(e) {
                e.preventDefault();
                e.currentTarget.classList.add('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                e.currentTarget.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.processFile(files[0]);
                }
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    this.processFile(file);
                }
            }

            processFile(file) {
                // Validate file
                if (!file.type.startsWith('image/')) {
                    this.showStatus('Please select an image file', 'error');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) { // 10MB
                    this.showStatus('File too large. Please select an image under 10MB', 'error');
                    return;
                }

                // Load image
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        this.uploadedImage = img;
                        this.showImagePreview(e.target.result);
                        this.enableGenerate();
                        this.updateCanvas();
                        this.showStatus('Image loaded successfully!', 'success');
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }

            showImagePreview(src) {
                const preview = document.getElementById('image-preview');
                const img = document.getElementById('preview-img');
                
                img.src = src;
                preview.classList.remove('hidden');
            }

            enableGenerate() {
                document.getElementById('generate-btn').disabled = false;
            }

            drawEmptySheet() {
                this.ctx.fillStyle = '#f8f9fa';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                this.ctx.strokeStyle = '#ddd';
                this.ctx.setLineDash([5, 5]);
                this.ctx.strokeRect(10, 10, this.canvas.width - 20, this.canvas.height - 20);
                
                this.ctx.fillStyle = '#999';
                this.ctx.font = '20px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('Upload an image to see preview', this.canvas.width / 2, this.canvas.height / 2);
            }

            updateCanvas() {
                if (!this.uploadedImage) {
                    this.drawEmptySheet();
                    return;
                }

                // Clear canvas
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Calculate layout
                const layout = this.calculateLayout();
                
                // Draw images
                layout.positions.forEach(pos => {
                    this.ctx.drawImage(
                        this.uploadedImage,
                        pos.x, pos.y,
                        layout.imageWidth, layout.imageHeight
                    );
                });

                // Draw grid
                this.ctx.strokeStyle = '#eee';
                this.ctx.setLineDash([2, 2]);
                for (let i = 0; i <= layout.cols; i++) {
                    const x = i * layout.imageWidth;
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, this.canvas.height);
                    this.ctx.stroke();
                }
                for (let i = 0; i <= layout.rows; i++) {
                    const y = i * layout.imageHeight;
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(this.canvas.width, y);
                    this.ctx.stroke();
                }
            }

            calculateLayout() {
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;
                
                // Calculate optimal grid
                const aspectRatio = this.uploadedImage.width / this.uploadedImage.height;
                const totalArea = canvasWidth * canvasHeight;
                const imageArea = totalArea / this.quantity;
                
                let imageHeight = Math.sqrt(imageArea / aspectRatio);
                let imageWidth = imageHeight * aspectRatio;
                
                const cols = Math.floor(canvasWidth / imageWidth);
                const rows = Math.ceil(this.quantity / cols);
                
                // Adjust to fit
                imageWidth = canvasWidth / cols;
                imageHeight = canvasHeight / rows;
                
                // Maintain aspect ratio
                if (imageWidth / imageHeight > aspectRatio) {
                    imageWidth = imageHeight * aspectRatio;
                } else {
                    imageHeight = imageWidth / aspectRatio;
                }

                // Generate positions
                const positions = [];
                for (let i = 0; i < this.quantity; i++) {
                    const col = i % cols;
                    const row = Math.floor(i / cols);
                    positions.push({
                        x: col * (canvasWidth / cols),
                        y: row * (canvasHeight / rows)
                    });
                }

                return { positions, imageWidth, imageHeight, cols, rows };
            }

            generateGangSheet() {
                if (!this.uploadedImage) {
                    this.showStatus('Please upload an image first', 'error');
                    return;
                }

                this.updateCanvas();
                this.showStatus('Gang sheet generated!', 'success');
                
                // Show download button
                document.getElementById('download-btn').classList.remove('hidden');
            }

            downloadPDF() {
                // Convert canvas to image and trigger download
                const link = document.createElement('a');
                link.download = `gang-sheet-${this.sheetSize}-${this.quantity}copies.png`;
                link.href = this.canvas.toDataURL();
                link.click();
                
                this.showStatus('Download started!', 'success');
            }

            showStatus(message, type) {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');
                
                setTimeout(() => {
                    status.classList.add('hidden');
                }, 3000);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleGangBuilder();
        });
    </script>
</body>
</html>
