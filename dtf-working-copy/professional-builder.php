<?php
/**
 * Professional DTF Gang Sheet Builder - PHP Version
 * Enhanced with server-side processing and database integration
 */

// Include configuration and database abstraction
require_once 'includes/config.php';
require_once 'includes/database-abstraction.php';
require_once 'includes/functions.php';

// Initialize session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get or create user
$user = dtf_get_or_create_user();

// Get user projects for quick access
$recent_projects = [];
if ($user) {
    try {
        $db = dtf_db();
        $recent_projects = $db->fetchAll(
            'projects',
            ['user_id' => $user['id']],
            'updated_at DESC',
            5
        );
    } catch (Exception $e) {
        dtf_log('ERROR', 'Failed to load recent projects', ['error' => $e->getMessage()]);
    }
}

// Page title and meta
$page_title = "Professional DTF Gang Sheet Builder";
$page_description = "Industry-standard DTF gang sheet creation with auto-nesting and professional features";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    
    <!-- Professional DTF Builder Styles -->
    <link rel="stylesheet" href="assets/css/professional-builder.css">
    
    <!-- Fabric.js for advanced canvas manipulation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .canvas-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Professional Controls */
        .control-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .control-section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Upload Zone */
        .upload-zone {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .upload-zone:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }

        .upload-zone.has-files {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        /* Recent Projects */
        .recent-projects {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .project-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
        }

        .project-item:last-child {
            border-bottom: none;
        }

        .project-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .project-date {
            color: #6c757d;
            font-size: 0.75rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
            width: 100%;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .hidden { display: none; }

        /* Canvas */
        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
        }

        #gang-canvas {
            display: block;
            max-width: 100%;
            background: white;
        }

        .canvas-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 0.85rem;
            color: #666;
        }

        /* Status */
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.85rem;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Professional Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
        }

        /* Settings Grid */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Professional DTF Gang Sheet Builder</h1>
            <p>Industry-standard features for professional DTF printing</p>
            <?php if ($user): ?>
                <small>Welcome back, User #<?php echo $user['id']; ?> | Session: <?php echo substr($user['session_id'], 0, 8); ?>...</small>
            <?php endif; ?>
        </div>

        <div class="main-grid">
            <!-- Professional Sidebar -->
            <div class="sidebar">
                <!-- Recent Projects -->
                <?php if (!empty($recent_projects)): ?>
                <div class="control-section">
                    <div class="section-title">📂 Recent Projects</div>
                    <div class="recent-projects">
                        <?php foreach ($recent_projects as $project): ?>
                        <div class="project-item">
                            <div>
                                <div class="project-name"><?php echo htmlspecialchars($project['name']); ?></div>
                                <div class="project-date"><?php echo date('M j, Y', strtotime($project['updated_at'])); ?></div>
                            </div>
                            <button class="btn btn-sm" onclick="loadProject(<?php echo $project['id']; ?>)">Load</button>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- File Upload -->
                <div class="control-section">
                    <div class="section-title">📁 File Management</div>
                    <div id="upload-zone" class="upload-zone">
                        <div>📤 Drop files or click to upload</div>
                        <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                    </div>
                    <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">
                    
                    <div id="image-list" class="image-list hidden"></div>
                </div>

                <!-- Sheet Configuration -->
                <div class="control-section">
                    <div class="section-title">📐 Sheet Configuration</div>
                    <div class="control-group">
                        <label class="control-label">Sheet Size</label>
                        <select id="sheet-size" class="control-input">
                            <option value="22x12">22" × 12" (Standard)</option>
                            <option value="22x24">22" × 24"</option>
                            <option value="22x36">22" × 36"</option>
                            <option value="22x48">22" × 48"</option>
                            <option value="22x60">22" × 60"</option>
                            <option value="22x72" selected>22" × 72" (Popular)</option>
                            <option value="22x100">22" × 100"</option>
                            <option value="22x120">22" × 120" (Max)</option>
                        </select>
                    </div>

                    <div class="settings-grid">
                        <div class="control-group">
                            <label class="control-label">DPI</label>
                            <select id="dpi" class="control-input">
                                <option value="150">150 DPI</option>
                                <option value="300" selected>300 DPI</option>
                                <option value="600">600 DPI</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">Color Mode</label>
                            <select id="color-mode" class="control-input">
                                <option value="cmyk" selected>CMYK</option>
                                <option value="rgb">RGB</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Auto-Nesting Settings -->
                <div class="control-section">
                    <div class="section-title">🧩 Auto-Nesting Settings</div>
                    <div class="settings-grid">
                        <div class="control-group">
                            <label class="control-label">Spacing (mm)</label>
                            <input type="number" id="spacing" class="control-input" value="3" min="0" max="20" step="0.5">
                        </div>
                        <div class="control-group">
                            <label class="control-label">Bleed (mm)</label>
                            <input type="number" id="bleed" class="control-input" value="1" min="0" max="10" step="0.5">
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Nesting Algorithm</label>
                        <select id="nesting-algorithm" class="control-input">
                            <option value="efficiency" selected>Maximum Efficiency</option>
                            <option value="speed">Fastest Processing</option>
                            <option value="uniform">Uniform Spacing</option>
                            <option value="rows">Row-by-Row</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="auto-rotate" checked>
                        <label for="auto-rotate">Auto-rotate for optimal fit</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="maintain-aspect" checked>
                        <label for="maintain-aspect">Maintain aspect ratio</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="add-margins" checked>
                        <label for="add-margins">Add safety margins</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="add-crop-marks">
                        <label for="add-crop-marks">Add crop marks</label>
                    </div>
                </div>

                <!-- Actions -->
                <div class="control-section">
                    <div class="section-title">⚡ Actions</div>
                    <button id="auto-nest-btn" class="btn btn-primary" disabled>
                        🧩 Auto-Nest Images
                    </button>
                    <button id="optimize-btn" class="btn btn-primary" disabled>
                        ⚡ Optimize Layout
                    </button>
                    <button id="preview-btn" class="btn btn-primary" disabled>
                        👁️ Preview Print
                    </button>
                </div>

                <!-- Export Options -->
                <div class="control-section">
                    <div class="section-title">📤 Export Options</div>
                    <div class="control-group">
                        <label class="control-label">Export Format</label>
                        <select id="export-format" class="control-input">
                            <option value="png" selected>PNG (Recommended)</option>
                            <option value="pdf">PDF (Print Ready)</option>
                            <option value="jpg">JPEG (Compressed)</option>
                            <option value="tiff">TIFF (Professional)</option>
                        </select>
                    </div>

                    <button id="download-btn" class="btn btn-success hidden">
                        📥 Download Gang Sheet
                    </button>

                    <button id="save-project-btn" class="btn btn-primary">
                        💾 Save Project
                    </button>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <!-- Professional Stats -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="image-count">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <!-- Canvas Container -->
                <div class="canvas-container">
                    <canvas id="gang-canvas" width="800" height="600"></canvas>
                </div>

                <!-- Canvas Info -->
                <div class="canvas-info">
                    <span>Sheet: <span id="sheet-dimensions">22" × 72"</span></span>
                    <span>DPI: <span id="current-dpi">300</span></span>
                    <span>Status: <span id="canvas-status">Ready</span></span>
                </div>

                <!-- Status Messages -->
                <div id="status-message" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- Include Professional DTF Builder JavaScript -->
    <script src="assets/js/dtf-builder-core.js"></script>
    <script src="assets/js/dtf-builder-auto-nest.js"></script>

    <script>
        // Initialize Professional DTF Builder with PHP integration
        class ProfessionalDTFBuilder {
            constructor() {
                this.canvas = null;
                this.ctx = null;
                this.images = [];
                this.sheetSize = '22x72';
                this.dpi = 300;
                this.spacing = 3; // mm
                this.bleed = 1; // mm
                this.autoRotate = true;
                this.maintainAspect = true;
                this.addMargins = true;
                this.userId = <?php echo json_encode($user['id'] ?? null); ?>;
                this.sessionId = <?php echo json_encode($user['session_id'] ?? null); ?>;

                this.init();
            }

            init() {
                this.initCanvas();
                this.setupEvents();
                this.updateCanvas();
                this.updateStats();
                this.showStatus('Professional DTF Builder ready', 'info');
            }

            initCanvas() {
                this.canvas = document.getElementById('gang-canvas');
                this.ctx = this.canvas.getContext('2d');

                // Set canvas size based on sheet dimensions
                this.updateCanvasSize();
            }

            updateCanvasSize() {
                const [width, height] = this.sheetSize.split('x').map(Number);
                const scale = 8; // pixels per inch for display

                this.canvas.width = width * scale;
                this.canvas.height = height * scale;

                // Update display
                this.canvas.style.maxWidth = '100%';
                this.canvas.style.height = 'auto';
            }

            setupEvents() {
                // File upload
                const uploadZone = document.getElementById('upload-zone');
                const fileInput = document.getElementById('file-input');

                uploadZone.onclick = () => fileInput.click();
                uploadZone.ondragover = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#3498db';
                };
                uploadZone.ondragleave = () => {
                    uploadZone.style.borderColor = '#bdc3c7';
                };
                uploadZone.ondrop = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#bdc3c7';
                    this.handleFiles(e.dataTransfer.files);
                };
                fileInput.onchange = (e) => this.handleFiles(e.target.files);

                // Settings
                document.getElementById('sheet-size').onchange = (e) => {
                    this.sheetSize = e.target.value;
                    this.updateCanvasSize();
                    this.updateCanvas();
                };

                document.getElementById('dpi').onchange = (e) => {
                    this.dpi = parseInt(e.target.value);
                    document.getElementById('current-dpi').textContent = this.dpi;
                    this.updateCanvas();
                };

                document.getElementById('spacing').oninput = (e) => {
                    this.spacing = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('bleed').oninput = (e) => {
                    this.bleed = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('auto-rotate').onchange = (e) => {
                    this.autoRotate = e.target.checked;
                };

                document.getElementById('maintain-aspect').onchange = (e) => {
                    this.maintainAspect = e.target.checked;
                };

                document.getElementById('add-margins').onchange = (e) => {
                    this.addMargins = e.target.checked;
                };

                // Actions
                document.getElementById('auto-nest-btn').onclick = () => this.autoNest();
                document.getElementById('optimize-btn').onclick = () => this.optimizeLayout();
                document.getElementById('download-btn').onclick = () => this.downloadGangSheet();
                document.getElementById('save-project-btn').onclick = () => this.saveProject();
            }

            async handleFiles(files) {
                for (let file of files) {
                    if (file.type.startsWith('image/')) {
                        await this.addImage(file);
                    }
                }
            }

            async addImage(file) {
                return new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const img = new Image();
                        img.onload = () => {
                            const imageData = {
                                name: file.name,
                                src: e.target.result,
                                width: img.width,
                                height: img.height,
                                physicalWidth: img.width / this.dpi,
                                physicalHeight: img.height / this.dpi,
                                dpi: this.dpi,
                                maxCopies: 1,
                                element: img
                            };

                            this.images.push(imageData);
                            this.updateImageList();
                            this.enableButtons();
                            this.updateStats();

                            // Auto-nest when images are added
                            if (this.images.length > 0) {
                                this.updateCanvas();
                            }

                            this.showStatus(`Added: ${imageData.name}`, 'success');
                            resolve();
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                });
            }

            updateImageList() {
                const list = document.getElementById('image-list');
                const uploadZone = document.getElementById('upload-zone');

                if (this.images.length === 0) {
                    list.classList.add('hidden');
                    uploadZone.classList.remove('has-files');
                    return;
                }

                uploadZone.classList.add('has-files');
                list.classList.remove('hidden');

                list.innerHTML = this.images.map((img, index) => `
                    <div class="image-item">
                        <img src="${img.src}" class="image-thumb" alt="${img.name}">
                        <div class="image-info">
                            <div class="image-name">${img.name}</div>
                            <div class="image-size">${img.width}×${img.height}px (${img.physicalWidth.toFixed(2)}"×${img.physicalHeight.toFixed(2)}")</div>
                        </div>
                        <input type="number" class="quantity-input" value="${img.maxCopies}" min="1" max="500"
                               onchange="dtfBuilder.updateImageQuantity(${index}, this.value)">
                    </div>
                `).join('');
            }

            updateImageQuantity(index, quantity) {
                this.images[index].maxCopies = parseInt(quantity);
                this.updateStats();
            }

            enableButtons() {
                document.getElementById('auto-nest-btn').disabled = false;
                document.getElementById('optimize-btn').disabled = false;
                document.getElementById('preview-btn').disabled = false;
            }

            autoNest() {
                if (this.images.length === 0) {
                    this.showStatus('Please add images first', 'error');
                    return;
                }

                this.showStatus('Auto-nesting images...', 'info');

                // Professional auto-nesting algorithm
                const layout = this.calculateOptimalLayout();
                this.renderLayout(layout);

                document.getElementById('download-btn').classList.remove('hidden');
                this.showStatus(`Auto-nested ${layout.totalCopies} images with ${layout.efficiency}% efficiency`, 'success');
            }

            calculateOptimalLayout() {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;
                const scale = canvasWidth / sheetWidth;

                let currentX = this.mmToPixels(this.addMargins ? 5 : 0, scale);
                let currentY = this.mmToPixels(this.addMargins ? 5 : 0, scale);
                let rowHeight = 0;
                let totalCopies = 0;
                let placedImages = [];

                for (let img of this.images) {
                    for (let copy = 0; copy < img.maxCopies; copy++) {
                        const imgWidth = (img.width / this.dpi) * scale;
                        const imgHeight = (img.height / this.dpi) * scale;
                        const spacing = this.mmToPixels(this.spacing, scale);

                        // Check if image fits in current row
                        if (currentX + imgWidth > canvasWidth - this.mmToPixels(this.addMargins ? 5 : 0, scale)) {
                            // Move to next row
                            currentX = this.mmToPixels(this.addMargins ? 5 : 0, scale);
                            currentY += rowHeight + spacing;
                            rowHeight = 0;
                        }

                        // Check if image fits in sheet height
                        if (currentY + imgHeight > canvasHeight - this.mmToPixels(this.addMargins ? 5 : 0, scale)) {
                            break; // Can't fit more images
                        }

                        placedImages.push({
                            image: img,
                            x: currentX,
                            y: currentY,
                            width: imgWidth,
                            height: imgHeight
                        });

                        currentX += imgWidth + spacing;
                        rowHeight = Math.max(rowHeight, imgHeight);
                        totalCopies++;
                    }
                }

                // Calculate efficiency
                const totalImageArea = placedImages.reduce((sum, item) => sum + (item.width * item.height), 0);
                const sheetArea = canvasWidth * canvasHeight;
                const efficiency = Math.round((totalImageArea / sheetArea) * 100);

                return {
                    placedImages,
                    totalCopies,
                    efficiency
                };
            }

            renderLayout(layout) {
                // Clear canvas
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw background
                this.ctx.fillStyle = '#ffffff';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw margins if enabled
                if (this.addMargins) {
                    this.drawMargins();
                }

                // Draw images
                layout.placedImages.forEach(item => {
                    this.ctx.drawImage(item.image.element, item.x, item.y, item.width, item.height);
                });

                // Update stats
                document.getElementById('efficiency').textContent = layout.efficiency + '%';
                document.getElementById('total-copies').textContent = layout.totalCopies;
            }

            drawMargins() {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const scale = this.canvas.width / sheetWidth;
                const margin = this.mmToPixels(5, scale);

                this.ctx.strokeStyle = '#e74c3c';
                this.ctx.lineWidth = 1;
                this.ctx.setLineDash([5, 5]);
                this.ctx.strokeRect(margin, margin, this.canvas.width - 2 * margin, this.canvas.height - 2 * margin);
                this.ctx.setLineDash([]);
            }

            mmToPixels(mm, scale) {
                return (mm / 25.4) * this.dpi * scale;
            }

            updateCanvas() {
                if (this.images.length > 0) {
                    this.autoNest();
                } else {
                    this.drawEmptySheet();
                }
                this.updateSheetInfo();
            }

            drawEmptySheet() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = '#ffffff';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                this.ctx.fillStyle = '#7f8c8d';
                this.ctx.font = '18px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('Professional DTF Gang Sheet', this.canvas.width / 2, this.canvas.height / 2 - 10);
                this.ctx.fillText('Upload images to begin auto-nesting', this.canvas.width / 2, this.canvas.height / 2 + 20);
            }

            updateSheetInfo() {
                const [width, height] = this.sheetSize.split('x');
                document.getElementById('sheet-dimensions').textContent = `${width}" × ${height}"`;
            }

            updateStats() {
                document.getElementById('image-count').textContent = this.images.length;

                const totalCopies = this.images.reduce((sum, img) => sum + img.maxCopies, 0);
                document.getElementById('total-copies').textContent = totalCopies;
            }

            optimizeLayout() {
                this.showStatus('Optimizing layout for maximum efficiency...', 'info');
                // Advanced optimization would go here
                this.autoNest();
            }

            downloadGangSheet() {
                const link = document.createElement('a');
                link.download = `dtf-gang-sheet-${this.sheetSize}-${this.dpi}dpi.png`;
                link.href = this.canvas.toDataURL();
                link.click();

                this.showStatus('Gang sheet downloaded successfully!', 'success');
            }

            async saveProject() {
                if (this.images.length === 0) {
                    this.showStatus('Please add images before saving', 'error');
                    return;
                }

                const projectName = prompt('Enter project name:', `DTF Project ${new Date().toLocaleDateString()}`);
                if (!projectName) return;

                const projectData = {
                    name: projectName,
                    sheet_size: this.sheetSize,
                    dpi: this.dpi,
                    spacing: this.spacing,
                    bleed: this.bleed,
                    auto_rotate: this.autoRotate,
                    maintain_aspect: this.maintainAspect,
                    add_margins: this.addMargins,
                    images: this.images.map(img => ({
                        name: img.name,
                        width: img.width,
                        height: img.height,
                        max_copies: img.maxCopies
                    }))
                };

                try {
                    const response = await fetch('api/save-project.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(projectData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showStatus(`Project "${projectName}" saved successfully!`, 'success');
                    } else {
                        this.showStatus(`Failed to save project: ${result.message}`, 'error');
                    }
                } catch (error) {
                    this.showStatus('Error saving project. Please try again.', 'error');
                    console.error('Save project error:', error);
                }
            }

            showStatus(message, type = 'info') {
                const statusEl = document.getElementById('status-message');
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
                statusEl.classList.remove('hidden');

                document.getElementById('canvas-status').textContent = message;

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    statusEl.classList.add('hidden');
                }, 5000);
            }
        }

        // Initialize the builder
        const dtfBuilder = new ProfessionalDTFBuilder();

        // Global functions for PHP integration
        function loadProject(projectId) {
            // Implementation for loading projects from database
            console.log('Loading project:', projectId);
        }
    </script>
</body>
</html>
