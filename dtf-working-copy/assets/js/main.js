/**
 * DTF Gang Builder - Main JavaScript File
 * 
 * This file contains the core JavaScript functionality for the DTF Gang Builder application.
 */

class DTFGangBuilder {
    constructor() {
        this.canvas = null;
        this.uploadedImages = [];
        this.currentSheetSize = '30x72';
        this.sheetDimensions = null;
        this.isGridVisible = true;
        this.currentZoom = 1;
        this.currentProjectId = null;
        this.revisionHistory = [];
        this.currentRevision = 0;
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 20;
        this.autoSaveInterval = null;

        // Professional settings
        this.imageSpacing = 3; // mm
        this.bleedArea = 1; // mm
        this.nestingAlgorithm = 'efficiency';
        this.autoRotate = true;
        this.addCropMarks = false;
        this.safetyMargins = true;
        this.selectedObjects = [];

        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.setupEventListeners();
        this.initializeCanvas();
        this.loadSheetSize();
        
        console.log('DTF Gang Builder initialized');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // File upload events
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const uploadButton = document.getElementById('upload-button');

        if (uploadZone && fileInput) {
            // Drag and drop events
            uploadZone.addEventListener('dragover', this.handleDragOver.bind(this));
            uploadZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            uploadZone.addEventListener('drop', this.handleDrop.bind(this));
            
            // Click to upload
            uploadZone.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        }

        if (uploadButton) {
            uploadButton.addEventListener('click', () => fileInput.click());
        }

        // Sheet size selector
        const sizeSelector = document.getElementById('sheet-size');
        if (sizeSelector) {
            sizeSelector.addEventListener('change', this.handleSheetSizeChange.bind(this));
        }

        // Canvas tools
        const zoomInBtn = document.getElementById('zoom-in');
        const zoomOutBtn = document.getElementById('zoom-out');
        const zoomFitBtn = document.getElementById('zoom-fit');
        const toggleGridBtn = document.getElementById('toggle-grid');

        if (zoomInBtn) zoomInBtn.addEventListener('click', () => this.zoomCanvas(1.2));
        if (zoomOutBtn) zoomOutBtn.addEventListener('click', () => this.zoomCanvas(0.8));
        if (zoomFitBtn) zoomFitBtn.addEventListener('click', () => this.fitCanvasToView());
        if (toggleGridBtn) toggleGridBtn.addEventListener('click', () => this.toggleGrid());

        // Action buttons
        const saveBtn = document.getElementById('save-project');
        const generateBtn = document.getElementById('generate-pdf');
        const clearBtn = document.getElementById('clear-canvas');

        if (saveBtn) saveBtn.addEventListener('click', () => this.saveProject());
        if (generateBtn) generateBtn.addEventListener('click', () => this.generatePDF());
        if (clearBtn) clearBtn.addEventListener('click', () => this.clearCanvas());
    }

    /**
     * Initialize Fabric.js canvas
     */
    initializeCanvas() {
        const canvasElement = document.getElementById('design-canvas');
        if (!canvasElement) {
            console.error('Canvas element not found');
            return;
        }

        // Initialize Fabric.js canvas
        this.canvas = new fabric.Canvas('design-canvas', {
            backgroundColor: '#ffffff',
            selection: true,
            preserveObjectStacking: true
        });

        // Set initial canvas size
        this.updateCanvasSize();

        // Canvas event listeners
        this.canvas.on('object:added', () => this.updateCanvasState());
        this.canvas.on('object:removed', () => this.updateCanvasState());
        this.canvas.on('object:modified', () => this.updateCanvasState());
        this.canvas.on('selection:created', () => this.updateSelectionButtons());
        this.canvas.on('selection:updated', () => this.updateSelectionButtons());
        this.canvas.on('selection:cleared', () => this.updateSelectionButtons());

        // Start auto-save
        this.startAutoSave();
    }

    /**
     * Handle drag over event
     */
    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('dragover');
    }

    /**
     * Handle drag leave event
     */
    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');
    }

    /**
     * Handle drop event
     */
    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');

        const files = Array.from(e.dataTransfer.files);
        this.handleFiles(files);
    }

    /**
     * Handle file select event
     */
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.handleFiles(files);
    }

    /**
     * Handle selected files
     */
    handleFiles(files) {
        if (files.length === 0) return;

        // Validate files
        const validFiles = files.filter(file => this.validateFile(file));
        
        if (validFiles.length === 0) {
            this.showError('No valid image files selected');
            return;
        }

        // Upload files
        this.uploadFiles(validFiles);
    }

    /**
     * Validate file
     */
    validateFile(file) {
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp', 'image/svg+xml'];
        const maxSize = 100 * 1024 * 1024; // 100MB

        if (!allowedTypes.includes(file.type)) {
            this.showError(`File type not allowed: ${file.name}`);
            return false;
        }

        if (file.size > maxSize) {
            this.showError(`File too large: ${file.name}`);
            return false;
        }

        return true;
    }

    /**
     * Upload files to server
     */
    async uploadFiles(files) {
        const formData = new FormData();
        
        files.forEach((file, index) => {
            formData.append(`files[${index}]`, file);
        });

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('csrf_token', csrfToken);
        }

        try {
            this.showProgress(0);
            
            const response = await fetch('api/upload.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.handleUploadSuccess(result.data);
            } else {
                this.showError(result.error || 'Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showError('Upload failed: ' + error.message);
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Handle successful upload
     */
    handleUploadSuccess(uploadedFiles) {
        uploadedFiles.forEach(file => {
            this.uploadedImages.push(file);
            this.addImageToCanvas(file);
            this.addImageToList(file);
        });

        this.showSuccess(`${uploadedFiles.length} file(s) uploaded successfully`);
    }

    /**
     * Add image to canvas
     */
    addImageToCanvas(imageData) {
        fabric.Image.fromURL(imageData.url, (img) => {
            // Scale image to fit canvas if too large
            const maxWidth = this.canvas.width * 0.3;
            const maxHeight = this.canvas.height * 0.3;
            
            if (img.width > maxWidth || img.height > maxHeight) {
                const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                img.scale(scale);
            }

            // Position image
            img.set({
                left: Math.random() * (this.canvas.width - img.getScaledWidth()),
                top: Math.random() * (this.canvas.height - img.getScaledHeight()),
                selectable: true,
                hasControls: true,
                hasBorders: true
            });

            // Add custom properties
            img.set('imageId', imageData.id);
            img.set('originalWidth', imageData.width);
            img.set('originalHeight', imageData.height);

            this.canvas.add(img);
            this.canvas.renderAll();
        });
    }

    /**
     * Add image to file list
     */
    addImageToList(imageData) {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;

        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <img src="${imageData.thumbnail}" alt="${imageData.filename}" class="file-thumbnail">
            <div class="file-info">
                <div class="file-name">${imageData.original_filename}</div>
                <div class="file-size">${this.formatFileSize(imageData.file_size)}</div>
            </div>
            <div class="file-actions">
                <button class="btn-remove" onclick="dtfBuilder.removeImage(${imageData.id})">Remove</button>
            </div>
        `;

        fileList.appendChild(fileItem);
    }

    /**
     * Remove image
     */
    removeImage(imageId) {
        // Remove from canvas
        const objects = this.canvas.getObjects();
        objects.forEach(obj => {
            if (obj.imageId === imageId) {
                this.canvas.remove(obj);
            }
        });

        // Remove from uploaded images array
        this.uploadedImages = this.uploadedImages.filter(img => img.id !== imageId);

        // Remove from file list
        const fileList = document.getElementById('file-list');
        if (fileList) {
            const fileItems = fileList.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                const removeBtn = item.querySelector('.btn-remove');
                if (removeBtn && removeBtn.onclick.toString().includes(imageId)) {
                    item.remove();
                }
            });
        }

        this.canvas.renderAll();
    }

    /**
     * Handle sheet size change
     */
    handleSheetSizeChange(e) {
        this.currentSheetSize = e.target.value;
        this.loadSheetSize();
        this.updateCanvasSize();
    }

    /**
     * Load sheet size information
     */
    async loadSheetSize() {
        try {
            const response = await fetch(`api/sheet-info.php?size=${this.currentSheetSize}`);
            const result = await response.json();

            if (result.success) {
                this.sheetDimensions = result.data;
                this.updateSheetInfo();
            }
        } catch (error) {
            console.error('Failed to load sheet size:', error);
        }
    }

    /**
     * Update canvas size based on sheet dimensions
     */
    updateCanvasSize() {
        if (!this.canvas || !this.sheetDimensions) return;

        const containerWidth = document.querySelector('.canvas-container').clientWidth - 40;
        const containerHeight = 600;

        // Calculate canvas size maintaining aspect ratio
        const aspectRatio = this.sheetDimensions.width_inches / this.sheetDimensions.height_inches;
        
        let canvasWidth = containerWidth;
        let canvasHeight = canvasWidth / aspectRatio;

        if (canvasHeight > containerHeight) {
            canvasHeight = containerHeight;
            canvasWidth = canvasHeight * aspectRatio;
        }

        this.canvas.setDimensions({
            width: canvasWidth,
            height: canvasHeight
        });

        this.drawGrid();
        this.canvas.renderAll();
    }

    /**
     * Update sheet information display
     */
    updateSheetInfo() {
        const sizeInfo = document.getElementById('size-info');
        if (sizeInfo && this.sheetDimensions) {
            sizeInfo.innerHTML = `
                <strong>Dimensions:</strong> ${this.sheetDimensions.width_inches}" × ${this.sheetDimensions.height_inches}"<br>
                <strong>Pixels:</strong> ${this.sheetDimensions.width_pixels} × ${this.sheetDimensions.height_pixels}<br>
                <strong>DPI:</strong> ${this.sheetDimensions.dpi}
            `;
        }
    }

    /**
     * Draw grid on canvas
     */
    drawGrid() {
        if (!this.canvas || !this.isGridVisible) return;

        // Remove existing grid
        const objects = this.canvas.getObjects();
        objects.forEach(obj => {
            if (obj.isGrid) {
                this.canvas.remove(obj);
            }
        });

        const gridSpacing = 50; // pixels
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;

        // Vertical lines
        for (let i = 0; i <= canvasWidth; i += gridSpacing) {
            const line = new fabric.Line([i, 0, i, canvasHeight], {
                stroke: '#e0e0e0',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isGrid: true
            });
            this.canvas.add(line);
            this.canvas.sendToBack(line);
        }

        // Horizontal lines
        for (let i = 0; i <= canvasHeight; i += gridSpacing) {
            const line = new fabric.Line([0, i, canvasWidth, i], {
                stroke: '#e0e0e0',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isGrid: true
            });
            this.canvas.add(line);
            this.canvas.sendToBack(line);
        }
    }

    /**
     * Toggle grid visibility
     */
    toggleGrid() {
        this.isGridVisible = !this.isGridVisible;
        
        if (this.isGridVisible) {
            this.drawGrid();
        } else {
            const objects = this.canvas.getObjects();
            objects.forEach(obj => {
                if (obj.isGrid) {
                    this.canvas.remove(obj);
                }
            });
        }
        
        this.canvas.renderAll();
        
        const toggleBtn = document.getElementById('toggle-grid');
        if (toggleBtn) {
            toggleBtn.textContent = this.isGridVisible ? 'Hide Grid' : 'Show Grid';
        }
    }

    /**
     * Zoom canvas
     */
    zoomCanvas(factor) {
        this.currentZoom *= factor;
        this.canvas.setZoom(this.currentZoom);
        this.updateZoomDisplay();
    }

    /**
     * Fit canvas to view
     */
    fitCanvasToView() {
        this.currentZoom = 1;
        this.canvas.setZoom(1);
        this.updateZoomDisplay();
    }

    /**
     * Update zoom display
     */
    updateZoomDisplay() {
        const zoomLevel = document.getElementById('zoom-level');
        if (zoomLevel) {
            zoomLevel.textContent = Math.round(this.currentZoom * 100) + '%';
        }
    }

    /**
     * Update canvas state
     */
    updateCanvasState() {
        // Save current state to undo stack
        this.saveToUndoStack();

        // Clear redo stack when new changes are made
        this.redoStack = [];

        // Update UI buttons
        this.updateHistoryButtons();

        console.log('Canvas state updated');
    }

    /**
     * Save project
     */
    async saveProject() {
        const projectData = {
            name: 'Untitled Project',
            sheet_size: this.currentSheetSize,
            canvas_data: JSON.stringify(this.canvas.toJSON()),
            images: this.uploadedImages
        };

        try {
            const response = await fetch('api/save-project.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(projectData)
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('Project saved successfully');
            } else {
                this.showError(result.error || 'Failed to save project');
            }
        } catch (error) {
            console.error('Save error:', error);
            this.showError('Failed to save project');
        }
    }

    /**
     * Generate PDF
     */
    async generatePDF() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload at least one image');
            return;
        }

        const generateData = {
            sheet_size: this.currentSheetSize,
            canvas_data: JSON.stringify(this.canvas.toJSON()),
            images: this.uploadedImages
        };

        try {
            this.showProgress(0, 'Generating PDF...');

            const response = await fetch('api/generate-pdf.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(generateData)
            });

            const result = await response.json();

            if (result.success) {
                // Download the generated PDF
                window.open(result.data.download_url, '_blank');
                this.showSuccess('PDF generated successfully');
            } else {
                this.showError(result.error || 'Failed to generate PDF');
            }
        } catch (error) {
            console.error('Generate error:', error);
            this.showError('Failed to generate PDF');
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Clear canvas
     */
    clearCanvas() {
        if (confirm('Are you sure you want to clear the canvas? This action cannot be undone.')) {
            this.canvas.clear();
            this.canvas.backgroundColor = '#ffffff';
            this.uploadedImages = [];

            const fileList = document.getElementById('file-list');
            if (fileList) {
                fileList.innerHTML = '';
            }

            this.drawGrid();
            this.canvas.renderAll();
        }
    }

    /**
     * Auto arrange images with professional settings
     */
    async autoArrangeImages() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload some images first');
            return;
        }

        const arrangeData = {
            sheet_size: this.currentSheetSize,
            images: this.uploadedImages,
            spacing: this.imageSpacing,
            bleed: this.bleedArea,
            algorithm: this.nestingAlgorithm,
            auto_rotate: this.autoRotate,
            safety_margins: this.safetyMargins
        };

        try {
            this.showProgress(0, 'Calculating optimal arrangement...');

            const response = await fetch('api/auto-arrange.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(arrangeData)
            });

            const result = await response.json();

            if (result.success) {
                this.applyArrangement(result.data);
                this.showSuccess(`Arranged ${result.data.arranged_count} images with ${result.data.efficiency}% efficiency`);
            } else {
                this.showError(result.error || 'Failed to calculate arrangement');
            }
        } catch (error) {
            console.error('Auto-arrange error:', error);
            this.showError('Failed to calculate arrangement');
        } finally {
            this.hideProgress();
        }
    }

    /**
     * Optimize layout for maximum efficiency
     */
    async optimizeLayout() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload some images first');
            return;
        }

        // Temporarily enable all optimization settings
        const originalSettings = {
            autoRotate: this.autoRotate,
            algorithm: this.nestingAlgorithm
        };

        this.autoRotate = true;
        this.nestingAlgorithm = 'efficiency';

        try {
            await this.autoArrangeImages();
            this.showSuccess('Layout optimized for maximum efficiency!');
        } finally {
            // Restore original settings
            this.autoRotate = originalSettings.autoRotate;
            this.nestingAlgorithm = originalSettings.algorithm;
        }
    }

    /**
     * Apply arrangement to canvas
     */
    applyArrangement(arrangementData) {
        const objects = this.canvas.getObjects();

        // Clear existing images (keep grid)
        objects.forEach(obj => {
            if (!obj.isGrid) {
                this.canvas.remove(obj);
            }
        });

        // Apply new arrangement
        arrangementData.arranged.forEach(item => {
            const imageData = this.uploadedImages.find(img => img.id === item.image_id);
            if (imageData) {
                fabric.Image.fromURL(imageData.url, (img) => {
                    // Convert inches to pixels for canvas
                    const left = this.inchesToPixels(item.x);
                    const top = this.inchesToPixels(item.y);
                    const width = this.inchesToPixels(item.width);
                    const height = this.inchesToPixels(item.height);

                    img.set({
                        left: left,
                        top: top,
                        scaleX: width / img.width,
                        scaleY: height / img.height,
                        angle: item.rotation || 0,
                        selectable: true,
                        hasControls: true,
                        hasBorders: true
                    });

                    img.set('imageId', item.image_id);
                    img.set('originalWidth', imageData.width);
                    img.set('originalHeight', imageData.height);

                    this.canvas.add(img);
                });
            }
        });

        this.canvas.renderAll();

        // Show arrangement statistics
        if (arrangementData.not_arranged_count > 0) {
            this.showError(`${arrangementData.not_arranged_count} images could not fit on the sheet`);
        }
    }

    /**
     * Utility methods
     */
    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    /**
     * Convert inches to pixels
     */
    inchesToPixels(inches, dpi = 300) {
        return inches * dpi;
    }

    /**
     * Convert pixels to inches
     */
    pixelsToInches(pixels, dpi = 300) {
        return pixels / dpi;
    }

    showProgress(percent, message = 'Uploading...') {
        const progressContainer = document.getElementById('upload-progress');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressContainer) {
            progressContainer.style.display = 'block';
            if (progressFill) progressFill.style.width = percent + '%';
            if (progressText) progressText.textContent = message;
        }
    }

    hideProgress() {
        const progressContainer = document.getElementById('upload-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }

    showError(message) {
        alert('Error: ' + message);
        console.error(message);
    }

    showSuccess(message) {
        alert('Success: ' + message);
        console.log(message);
    }

    // =============================================================================
    // PROJECT HISTORY & UNDO/REDO FUNCTIONALITY
    // =============================================================================

    /**
     * Save current state to undo stack
     */
    saveToUndoStack() {
        if (!this.canvas) return;

        const state = {
            canvas: JSON.stringify(this.canvas.toJSON()),
            images: [...this.uploadedImages],
            sheetSize: this.currentSheetSize,
            timestamp: Date.now()
        };

        this.undoStack.push(state);

        // Limit undo stack size
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
    }

    /**
     * Undo last action
     */
    undo() {
        if (this.undoStack.length === 0) {
            this.showError('Nothing to undo');
            return;
        }

        // Save current state to redo stack
        const currentState = {
            canvas: JSON.stringify(this.canvas.toJSON()),
            images: [...this.uploadedImages],
            sheetSize: this.currentSheetSize,
            timestamp: Date.now()
        };
        this.redoStack.push(currentState);

        // Get previous state
        const previousState = this.undoStack.pop();
        this.restoreCanvasState(previousState);

        this.updateHistoryButtons();
        this.showSuccess('Undo completed');
    }

    /**
     * Redo last undone action
     */
    redo() {
        if (this.redoStack.length === 0) {
            this.showError('Nothing to redo');
            return;
        }

        // Save current state to undo stack
        this.saveToUndoStack();

        // Get next state
        const nextState = this.redoStack.pop();
        this.restoreCanvasState(nextState);

        this.updateHistoryButtons();
        this.showSuccess('Redo completed');
    }

    /**
     * Restore canvas state
     */
    restoreCanvasState(state) {
        try {
            // Clear canvas
            this.canvas.clear();
            this.canvas.backgroundColor = '#ffffff';

            // Restore canvas objects
            const canvasData = JSON.parse(state.canvas);
            this.canvas.loadFromJSON(canvasData, () => {
                this.canvas.renderAll();
                this.drawGrid();
            });

            // Restore other state
            this.uploadedImages = [...state.images];
            this.currentSheetSize = state.sheetSize;

            // Update UI
            const sizeSelector = document.getElementById('sheet-size');
            if (sizeSelector) {
                sizeSelector.value = this.currentSheetSize;
            }

            this.loadSheetSize();
            this.updateFileList();

        } catch (error) {
            console.error('Failed to restore canvas state:', error);
            this.showError('Failed to restore previous state');
        }
    }

    /**
     * Update file list display
     */
    updateFileList() {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;

        fileList.innerHTML = '';
        this.uploadedImages.forEach(imageData => {
            this.addImageToList(imageData);
        });
    }

    /**
     * Update history buttons state
     */
    updateHistoryButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');

        if (undoBtn) {
            undoBtn.disabled = this.undoStack.length === 0;
            undoBtn.title = `Undo (${this.undoStack.length} actions available)`;
        }

        if (redoBtn) {
            redoBtn.disabled = this.redoStack.length === 0;
            redoBtn.title = `Redo (${this.redoStack.length} actions available)`;
        }
    }

    /**
     * Start auto-save functionality
     */
    startAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            if (this.currentProjectId && this.uploadedImages.length > 0) {
                this.autoSaveProject();
            }
        }, 30000);
    }

    /**
     * Auto-save project
     */
    async autoSaveProject() {
        if (!this.currentProjectId) return;

        try {
            await this.saveRevision('auto_save', 'Auto-save');
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }

    /**
     * Save project revision
     */
    async saveRevision(changeType = 'manual', description = '') {
        if (!this.currentProjectId) {
            this.showError('No active project to save revision');
            return;
        }

        const revisionData = {
            project_id: this.currentProjectId,
            canvas_data: JSON.stringify(this.canvas.toJSON()),
            images_data: JSON.stringify(this.uploadedImages),
            sheet_size: this.currentSheetSize,
            description: description,
            change_type: changeType
        };

        try {
            const response = await fetch('api/project-history.php?action=save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(revisionData)
            });

            const result = await response.json();

            if (result.success) {
                this.currentRevision = result.data.revision_number;
                if (changeType === 'manual') {
                    this.showSuccess('Revision saved successfully');
                }
                return result.data;
            } else {
                throw new Error(result.error || 'Failed to save revision');
            }
        } catch (error) {
            console.error('Save revision error:', error);
            if (changeType === 'manual') {
                this.showError('Failed to save revision: ' + error.message);
            }
            throw error;
        }
    }

    /**
     * Load revision history
     */
    async loadRevisionHistory() {
        if (!this.currentProjectId) {
            this.showError('No active project');
            return;
        }

        try {
            const response = await fetch(`api/project-history.php?action=list&project_id=${this.currentProjectId}&limit=20`);
            const result = await response.json();

            if (result.success) {
                this.revisionHistory = result.data.revisions;
                this.displayRevisionHistory();
                return result.data;
            } else {
                throw new Error(result.error || 'Failed to load revision history');
            }
        } catch (error) {
            console.error('Load revision history error:', error);
            this.showError('Failed to load revision history: ' + error.message);
            throw error;
        }
    }

    /**
     * Display revision history
     */
    displayRevisionHistory() {
        const revisionList = document.getElementById('revision-list');
        const currentRevisionSpan = document.getElementById('current-revision-number');

        if (!revisionList) return;

        // Update current revision number
        if (currentRevisionSpan) {
            currentRevisionSpan.textContent = this.currentRevision || '-';
        }

        if (this.revisionHistory.length === 0) {
            revisionList.innerHTML = '<div class="loading-message">No revision history available</div>';
            return;
        }

        let html = '';
        this.revisionHistory.forEach(revision => {
            const isCurrent = revision.revision_number === this.currentRevision;
            const date = new Date(revision.created_at);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

            html += `
                <div class="revision-item ${isCurrent ? 'current' : ''}">
                    <div class="revision-info">
                        <div class="revision-number">
                            Revision #${revision.revision_number}
                            ${isCurrent ? '(Current)' : ''}
                        </div>
                        <div class="revision-description">
                            ${revision.change_description || 'No description'}
                        </div>
                        <div class="revision-meta">
                            ${formattedDate}
                            <span class="revision-type ${revision.change_type}">${revision.change_type}</span>
                        </div>
                    </div>
                    <div class="revision-actions">
                        <button class="btn-restore"
                                onclick="window.dtfBuilder.restoreToRevision(${revision.revision_number})"
                                ${isCurrent ? 'disabled' : ''}>
                            ${isCurrent ? 'Current' : 'Restore'}
                        </button>
                    </div>
                </div>
            `;
        });

        revisionList.innerHTML = html;
    }

    /**
     * Restore to specific revision
     */
    async restoreToRevision(revisionNumber) {
        if (!this.currentProjectId) {
            this.showError('No active project');
            return;
        }

        if (!confirm(`Are you sure you want to restore to revision #${revisionNumber}? This will create a new revision with the restored state.`)) {
            return;
        }

        try {
            this.showProgress(0, 'Restoring revision...');

            const response = await fetch('api/project-history.php?action=restore', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    project_id: this.currentProjectId,
                    revision_number: revisionNumber
                })
            });

            const result = await response.json();

            if (result.success) {
                // Restore the canvas state
                const restoredData = result.data.restored_revision;

                this.canvas.loadFromJSON(restoredData.canvas_data, () => {
                    this.canvas.renderAll();
                    this.drawGrid();
                });

                if (restoredData.images_data) {
                    this.uploadedImages = restoredData.images_data;
                    this.updateFileList();
                }

                this.currentSheetSize = restoredData.sheet_size;
                this.currentRevision = result.data.new_revision.revision_number;

                // Update UI
                const sizeSelector = document.getElementById('sheet-size');
                if (sizeSelector) {
                    sizeSelector.value = this.currentSheetSize;
                }

                this.loadSheetSize();
                this.showSuccess(`Restored to revision #${revisionNumber}`);
            } else {
                throw new Error(result.error || 'Failed to restore revision');
            }
        } catch (error) {
            console.error('Restore revision error:', error);
            this.showError('Failed to restore revision: ' + error.message);
        } finally {
            this.hideProgress();
        }
    }

    // =============================================================================
    // PROFESSIONAL OBJECT MANIPULATION
    // =============================================================================

    /**
     * Update selection buttons based on selected objects
     */
    updateSelectionButtons() {
        const activeSelection = this.canvas.getActiveObject();
        const hasSelection = activeSelection && !activeSelection.isGrid;

        const duplicateBtn = document.getElementById('duplicate-selected');
        const deleteBtn = document.getElementById('delete-selected');

        if (duplicateBtn) duplicateBtn.disabled = !hasSelection;
        if (deleteBtn) deleteBtn.disabled = !hasSelection;
    }

    /**
     * Duplicate selected objects
     */
    duplicateSelected() {
        const activeObject = this.canvas.getActiveObject();
        if (!activeObject || activeObject.isGrid) {
            this.showError('Please select an object to duplicate');
            return;
        }

        activeObject.clone((cloned) => {
            cloned.set({
                left: cloned.left + 20,
                top: cloned.top + 20,
                evented: true,
            });

            if (cloned.type === 'activeSelection') {
                // Handle multiple selection
                cloned.canvas = this.canvas;
                cloned.forEachObject((obj) => {
                    this.canvas.add(obj);
                });
                cloned.setCoords();
            } else {
                this.canvas.add(cloned);
            }

            this.canvas.setActiveObject(cloned);
            this.canvas.requestRenderAll();
            this.showSuccess('Object duplicated');
        });
    }

    /**
     * Delete selected objects
     */
    deleteSelected() {
        const activeObject = this.canvas.getActiveObject();
        if (!activeObject || activeObject.isGrid) {
            this.showError('Please select an object to delete');
            return;
        }

        if (activeObject.type === 'activeSelection') {
            // Handle multiple selection
            activeObject.forEachObject((obj) => {
                this.canvas.remove(obj);
            });
        } else {
            this.canvas.remove(activeObject);
        }

        this.canvas.discardActiveObject();
        this.canvas.requestRenderAll();
        this.showSuccess('Object deleted');
    }

    /**
     * Add crop marks to canvas
     */
    addCropMarks() {
        if (!this.addCropMarks) return;

        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        const markLength = 20;
        const markOffset = 10;

        // Remove existing crop marks
        this.removeCropMarks();

        // Create crop mark lines
        const cropMarks = [];

        // Top-left corner
        cropMarks.push(
            new fabric.Line([markOffset, markOffset + markLength, markOffset, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([markOffset, markOffset, markOffset + markLength, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Top-right corner
        cropMarks.push(
            new fabric.Line([canvasWidth - markOffset, markOffset + markLength, canvasWidth - markOffset, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([canvasWidth - markOffset, markOffset, canvasWidth - markOffset - markLength, markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Bottom-left corner
        cropMarks.push(
            new fabric.Line([markOffset, canvasHeight - markOffset - markLength, markOffset, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([markOffset, canvasHeight - markOffset, markOffset + markLength, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Bottom-right corner
        cropMarks.push(
            new fabric.Line([canvasWidth - markOffset, canvasHeight - markOffset - markLength, canvasWidth - markOffset, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            }),
            new fabric.Line([canvasWidth - markOffset, canvasHeight - markOffset, canvasWidth - markOffset - markLength, canvasHeight - markOffset], {
                stroke: '#000',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                isCropMark: true
            })
        );

        // Add all crop marks to canvas
        cropMarks.forEach(mark => this.canvas.add(mark));
        this.canvas.renderAll();
    }

    /**
     * Remove crop marks from canvas
     */
    removeCropMarks() {
        const objects = this.canvas.getObjects();
        const cropMarks = objects.filter(obj => obj.isCropMark);
        cropMarks.forEach(mark => this.canvas.remove(mark));
    }

    /**
     * Apply professional spacing between objects
     */
    applyProfessionalSpacing() {
        const objects = this.canvas.getObjects().filter(obj => !obj.isGrid && !obj.isCropMark);
        if (objects.length < 2) return;

        const spacingPixels = this.mmToPixels(this.imageSpacing);

        // Sort objects by position
        objects.sort((a, b) => {
            if (Math.abs(a.top - b.top) < 10) {
                return a.left - b.left;
            }
            return a.top - b.top;
        });

        // Apply spacing
        let currentX = this.safetyMargins ? this.mmToPixels(5) : 0;
        let currentY = this.safetyMargins ? this.mmToPixels(5) : 0;
        let rowHeight = 0;

        objects.forEach(obj => {
            // Check if object fits in current row
            if (currentX + obj.getScaledWidth() > this.canvas.width - (this.safetyMargins ? this.mmToPixels(5) : 0)) {
                // Move to next row
                currentX = this.safetyMargins ? this.mmToPixels(5) : 0;
                currentY += rowHeight + spacingPixels;
                rowHeight = 0;
            }

            obj.set({
                left: currentX,
                top: currentY
            });

            currentX += obj.getScaledWidth() + spacingPixels;
            rowHeight = Math.max(rowHeight, obj.getScaledHeight());
        });

        this.canvas.renderAll();
    }

    /**
     * Convert millimeters to pixels
     */
    mmToPixels(mm) {
        // Assuming 300 DPI and 25.4 mm per inch
        return (mm / 25.4) * 300 * (this.canvas.width / (this.sheetDimensions?.width || 30));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if Fabric.js is loaded
    if (typeof fabric === 'undefined') {
        console.error('Fabric.js is not loaded. Please include the Fabric.js library.');
        return;
    }

    window.dtfBuilder = new DTFGangBuilder();
});
