<?php
/**
 * DTF Gang Builder - Sheet Information API
 * 
 * This endpoint provides information about available sheet sizes and their dimensions.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    dtf_error_response('Method not allowed', 405);
}

try {
    $sheet_size = $_GET['size'] ?? DTF_DEFAULT_SHEET_SIZE;
    
    // Validate sheet size
    if (!dtf_validate_sheet_size($sheet_size)) {
        dtf_error_response('Invalid sheet size');
    }
    
    // Get sheet dimensions
    $dimensions = dtf_get_sheet_dimensions($sheet_size);
    
    if (!$dimensions) {
        dtf_error_response('Failed to get sheet dimensions');
    }
    
    // Add additional information
    $sheet_info = [
        'size' => $sheet_size,
        'width_inches' => $dimensions['width_inches'],
        'height_inches' => $dimensions['height_inches'],
        'width_pixels' => $dimensions['width_pixels'],
        'height_pixels' => $dimensions['height_pixels'],
        'dpi' => $dimensions['dpi'],
        'aspect_ratio' => round($dimensions['width_inches'] / $dimensions['height_inches'], 4),
        'area_square_inches' => round($dimensions['width_inches'] * $dimensions['height_inches'], 2),
        'bleed_inches' => DTF_SHEET_BLEED,
        'margin_inches' => DTF_SHEET_MARGIN,
        'printable_width' => $dimensions['width_inches'] - (2 * DTF_SHEET_MARGIN),
        'printable_height' => $dimensions['height_inches'] - (2 * DTF_SHEET_MARGIN)
    ];
    
    dtf_success_response($sheet_info, 'Sheet information retrieved successfully');
    
} catch (Exception $e) {
    dtf_log('ERROR', 'Sheet info API error', [
        'error' => $e->getMessage(),
        'sheet_size' => $_GET['size'] ?? 'not provided'
    ]);
    
    dtf_error_response('Failed to get sheet information: ' . $e->getMessage(), 500);
}
?>
