<?php
/**
 * DTF Gang Builder - Enhanced Upload API
 * 
 * Advanced file upload handling with support for multiple formats,
 * batch processing, and comprehensive validation.
 */

// Define application constant
define('DTF_GANG_BUILDER', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/file-handler.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    dtf_error_response('Method not allowed', 405);
}

try {
    // Verify CSRF token
    $csrf_token = $_POST['csrf_token'] ?? '';
    if (!dtf_verify_csrf_token($csrf_token)) {
        dtf_error_response('Invalid CSRF token', 403);
    }

    // Get or create user
    $user = dtf_get_or_create_user();
    if (!$user) {
        dtf_error_response('User session not found', 401);
    }

    // Check if files were uploaded
    if (!isset($_FILES['files']) || empty($_FILES['files']['name'][0])) {
        dtf_error_response('No files uploaded');
    }

    // Use enhanced file handler for processing
    $file_handler = new DTF_FileHandler();
    $processing_result = $file_handler->processUpload($_FILES['files']);

    if (!$processing_result['success']) {
        dtf_error_response('File processing failed');
    }

    // Store file information in database
    $db = DTF_Database::getInstance();
    $uploaded_files = [];
    $errors = [];

    // Get or create a project for these uploads
    $project_id = getOrCreateProject($user, $db);

    foreach ($processing_result['results'] as $file_result) {
        if ($file_result['success']) {
            try {
                // Store in database
                $file_data = [
                    'project_id' => $project_id,
                    'user_id' => $user['id'],
                    'filename' => $file_result['filename'],
                    'original_name' => $file_result['original_name'],
                    'file_path' => $file_result['path'],
                    'file_url' => $file_result['url'],
                    'thumbnail_path' => $file_result['thumbnail']['path'] ?? null,
                    'thumbnail_url' => $file_result['thumbnail']['url'] ?? null,
                    'width' => $file_result['width'],
                    'height' => $file_result['height'],
                    'file_size' => $file_result['size'],
                    'mime_type' => $file_result['mime_type'],
                    'format' => $file_result['format'],
                    'dpi' => $file_result['dpi'],
                    'color_space' => $file_result['color_space'],
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $file_id = $db->insert('uploaded_files', $file_data);

                // Prepare response data
                $uploaded_files[] = [
                    'id' => $file_result['id'],
                    'db_id' => $file_id,
                    'filename' => $file_result['filename'],
                    'original_name' => $file_result['original_name'],
                    'url' => $file_result['url'],
                    'thumbnail' => $file_result['thumbnail']['url'] ?? $file_result['url'],
                    'width' => $file_result['width'],
                    'height' => $file_result['height'],
                    'size' => $file_result['size'],
                    'format' => $file_result['format'],
                    'mime_type' => $file_result['mime_type'],
                    'dpi' => $file_result['dpi'],
                    'color_space' => $file_result['color_space'],
                    'project_id' => $project_id,
                    'created_at' => $file_result['created_at']
                ];

            } catch (Exception $e) {
                dtf_log('ERROR', 'Database storage failed', [
                    'file' => $file_result['filename'],
                    'error' => $e->getMessage(),
                    'user_id' => $user['id']
                ]);

                $errors[] = [
                    'file' => $file_result['original_name'],
                    'error' => 'Database storage failed: ' . $e->getMessage()
                ];
            }
        } else {
            $errors[] = [
                'file' => $file_result['filename'] ?? 'unknown',
                'error' => $file_result['error']
            ];
        }
    }

    // Prepare comprehensive response
    $response = [
        'success' => true,
        'data' => [
            'files' => $uploaded_files,
            'project_id' => $project_id,
            'statistics' => [
                'total_uploaded' => count($uploaded_files),
                'total_failed' => count($errors),
                'total_processed' => $processing_result['processed'],
                'formats_detected' => array_unique(array_column($uploaded_files, 'format')),
                'total_size' => array_sum(array_column($uploaded_files, 'size')),
                'average_dpi' => count($uploaded_files) > 0 ? 
                    round(array_sum(array_column($uploaded_files, 'dpi')) / count($uploaded_files)) : 0
            ]
        ],
        'message' => generateUploadMessage(count($uploaded_files), count($errors))
    ];

    if (!empty($errors)) {
        $response['errors'] = $errors;
    }

    // Log comprehensive upload summary
    dtf_log('INFO', 'Enhanced upload completed', [
        'user_id' => $user['id'],
        'project_id' => $project_id,
        'total_files' => $processing_result['total_files'],
        'successful' => count($uploaded_files),
        'failed' => count($errors),
        'formats' => $response['data']['statistics']['formats_detected'],
        'total_size' => $response['data']['statistics']['total_size']
    ]);

    dtf_success_response($response['data'], $response['message']);

} catch (Exception $e) {
    dtf_log('ERROR', 'Enhanced upload API error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'user_id' => $user['id'] ?? null
    ]);

    dtf_error_response('Upload failed: ' . $e->getMessage(), 500);
}

/**
 * Get or create a project for file uploads
 */
function getOrCreateProject($user, $db) {
    // Check for existing draft project
    $existing_project = $db->fetch(
        "SELECT id FROM " . DTF_DB_PREFIX . "projects 
         WHERE user_id = :user_id AND status = 'draft' 
         ORDER BY created_at DESC LIMIT 1",
        ['user_id' => $user['id']]
    );

    if ($existing_project) {
        return $existing_project['id'];
    }

    // Create new project
    $project_data = [
        'user_id' => $user['id'],
        'project_uuid' => dtf_generate_uuid(),
        'name' => 'New Gang Sheet - ' . date('Y-m-d H:i'),
        'sheet_size' => DTF_DEFAULT_SHEET_SIZE,
        'status' => 'draft',
        'created_at' => date('Y-m-d H:i:s')
    ];

    return $db->insert('projects', $project_data);
}

/**
 * Generate upload success message
 */
function generateUploadMessage($successful, $failed) {
    $message = '';
    
    if ($successful > 0) {
        $message .= $successful . ' file' . ($successful > 1 ? 's' : '') . ' uploaded successfully';
    }
    
    if ($failed > 0) {
        if ($successful > 0) {
            $message .= ', ';
        }
        $message .= $failed . ' file' . ($failed > 1 ? 's' : '') . ' failed';
    }
    
    if ($successful === 0 && $failed === 0) {
        $message = 'No files processed';
    }
    
    return $message;
}

/**
 * Create uploaded_files table if it doesn't exist
 */
function ensureUploadedFilesTable() {
    $db = DTF_Database::getInstance();
    
    $sql = "CREATE TABLE IF NOT EXISTS " . DTF_DB_PREFIX . "uploaded_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        filename TEXT NOT NULL,
        original_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_url TEXT NOT NULL,
        thumbnail_path TEXT NULL,
        thumbnail_url TEXT NULL,
        width INTEGER NOT NULL,
        height INTEGER NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,
        format TEXT NOT NULL,
        dpi INTEGER DEFAULT 300,
        color_space TEXT DEFAULT 'RGB',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES " . DTF_DB_PREFIX . "projects(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES " . DTF_DB_PREFIX . "users(id) ON DELETE CASCADE
    )";
    
    $db->query($sql);
    
    // Create indexes
    $db->query("CREATE INDEX IF NOT EXISTS idx_uploaded_files_project ON " . DTF_DB_PREFIX . "uploaded_files(project_id)");
    $db->query("CREATE INDEX IF NOT EXISTS idx_uploaded_files_user ON " . DTF_DB_PREFIX . "uploaded_files(user_id)");
    $db->query("CREATE INDEX IF NOT EXISTS idx_uploaded_files_format ON " . DTF_DB_PREFIX . "uploaded_files(format)");
}

// Ensure table exists
ensureUploadedFilesTable();

?>
