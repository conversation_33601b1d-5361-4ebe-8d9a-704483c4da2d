<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional DTF Gang Sheet Builder</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .canvas-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Professional Controls */
        .control-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .control-section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Upload Zone */
        .upload-zone {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .upload-zone:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }

        .upload-zone.has-files {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        /* Image List */
        .image-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
        }

        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
        }

        .image-item:last-child {
            border-bottom: none;
        }

        .image-thumb {
            width: 30px;
            height: 30px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }

        .image-info {
            flex: 1;
        }

        .image-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .image-size {
            color: #7f8c8d;
            font-size: 0.75rem;
        }

        .quantity-input {
            width: 50px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
            font-size: 0.8rem;
        }

        /* Professional Settings */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Canvas */
        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
        }

        #gang-canvas {
            display: block;
            max-width: 100%;
            background: white;
        }

        .canvas-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 0.85rem;
            color: #666;
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
            width: 100%;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .hidden { display: none; }

        /* Status */
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.85rem;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Professional Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Professional DTF Gang Sheet Builder</h1>
            <p>Industry-standard features for professional DTF printing</p>
        </div>

        <div class="main-grid">
            <!-- Professional Sidebar -->
            <div class="sidebar">
                <!-- File Upload -->
                <div class="control-section">
                    <div class="section-title">📁 File Management</div>
                    <div id="upload-zone" class="upload-zone">
                        <div>📤 Drop files or click to upload</div>
                        <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                    </div>
                    <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">
                    
                    <div id="image-list" class="image-list hidden"></div>
                </div>

                <!-- Sheet Configuration -->
                <div class="control-section">
                    <div class="section-title">📐 Sheet Configuration</div>
                    <div class="control-group">
                        <label class="control-label">Sheet Size</label>
                        <select id="sheet-size" class="control-input">
                            <option value="22x12">22" × 12" (Standard)</option>
                            <option value="22x24">22" × 24"</option>
                            <option value="22x36">22" × 36"</option>
                            <option value="22x48">22" × 48"</option>
                            <option value="22x60">22" × 60"</option>
                            <option value="22x72" selected>22" × 72" (Popular)</option>
                            <option value="22x100">22" × 100"</option>
                            <option value="22x120">22" × 120" (Max)</option>
                        </select>
                    </div>
                    
                    <div class="settings-grid">
                        <div class="control-group">
                            <label class="control-label">DPI</label>
                            <select id="dpi" class="control-input">
                                <option value="150">150 DPI</option>
                                <option value="300" selected>300 DPI</option>
                                <option value="600">600 DPI</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">Color Mode</label>
                            <select id="color-mode" class="control-input">
                                <option value="cmyk" selected>CMYK</option>
                                <option value="rgb">RGB</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Auto-Nesting Settings -->
                <div class="control-section">
                    <div class="section-title">🧩 Auto-Nesting Settings</div>
                    <div class="settings-grid">
                        <div class="control-group">
                            <label class="control-label">Spacing (mm)</label>
                            <input type="number" id="spacing" class="control-input" value="3" min="0" max="20" step="0.5">
                        </div>
                        <div class="control-group">
                            <label class="control-label">Bleed (mm)</label>
                            <input type="number" id="bleed" class="control-input" value="1" min="0" max="10" step="0.5">
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Nesting Algorithm</label>
                        <select id="nesting-algorithm" class="control-input">
                            <option value="efficiency" selected>Maximum Efficiency</option>
                            <option value="speed">Fastest Processing</option>
                            <option value="uniform">Uniform Spacing</option>
                            <option value="rows">Row-by-Row</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="auto-rotate" checked>
                        <label for="auto-rotate">Auto-rotate for optimal fit</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="maintain-aspect" checked>
                        <label for="maintain-aspect">Maintain aspect ratio</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="add-margins" checked>
                        <label for="add-margins">Add safety margins</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="add-crop-marks">
                        <label for="add-crop-marks">Add crop marks</label>
                    </div>
                </div>

                <!-- Image Processing -->
                <div class="control-section">
                    <div class="section-title">🎨 Image Processing</div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="auto-enhance">
                        <label for="auto-enhance">Auto-enhance images</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="remove-background">
                        <label for="remove-background">Auto-remove backgrounds</label>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Image Quality</label>
                        <select id="image-quality" class="control-input">
                            <option value="draft">Draft (Fast)</option>
                            <option value="standard" selected>Standard</option>
                            <option value="high">High Quality</option>
                            <option value="maximum">Maximum (Slow)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Resize Mode</label>
                        <select id="resize-mode" class="control-input">
                            <option value="none" selected>Keep Original Size</option>
                            <option value="fit">Fit to Standard Sizes</option>
                            <option value="uniform">Make All Same Size</option>
                            <option value="optimize">Optimize for Sheet</option>
                        </select>
                    </div>
                </div>

                <!-- Actions -->
                <div class="control-section">
                    <div class="section-title">⚡ Actions</div>
                    <button id="auto-nest-btn" class="btn btn-primary" disabled>
                        🧩 Auto-Nest Images
                    </button>
                    <button id="optimize-btn" class="btn btn-primary" disabled>
                        ⚡ Optimize Layout
                    </button>
                    <button id="preview-btn" class="btn btn-primary" disabled>
                        👁️ Preview Print
                    </button>
                </div>

                <!-- Export Options -->
                <div class="control-section">
                    <div class="section-title">📤 Export Options</div>
                    <div class="control-group">
                        <label class="control-label">Export Format</label>
                        <select id="export-format" class="control-input">
                            <option value="png" selected>PNG (Recommended)</option>
                            <option value="pdf">PDF (Print Ready)</option>
                            <option value="jpg">JPEG (Compressed)</option>
                            <option value="tiff">TIFF (Professional)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Export DPI</label>
                        <select id="export-dpi" class="control-input">
                            <option value="150">150 DPI (Draft)</option>
                            <option value="300" selected>300 DPI (Standard)</option>
                            <option value="600">600 DPI (High Quality)</option>
                            <option value="1200">1200 DPI (Maximum)</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="include-bleed" checked>
                        <label for="include-bleed">Include bleed area</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="include-marks">
                        <label for="include-marks">Include cut marks</label>
                    </div>

                    <button id="download-btn" class="btn btn-success hidden">
                        📥 Download Gang Sheet
                    </button>

                    <button id="save-project-btn" class="btn btn-primary hidden">
                        💾 Save Project
                    </button>
                </div>

                <!-- Quick Presets -->
                <div class="control-section">
                    <div class="section-title">⚡ Quick Presets</div>
                    <button class="btn btn-primary preset-btn" data-preset="standard">
                        📋 Standard DTF
                    </button>
                    <button class="btn btn-primary preset-btn" data-preset="high-efficiency">
                        🎯 High Efficiency
                    </button>
                    <button class="btn btn-primary preset-btn" data-preset="production">
                        🏭 Production Mode
                    </button>
                    <button class="btn btn-primary preset-btn" data-preset="custom">
                        ⚙️ Custom Setup
                    </button>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-images">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gang-canvas" width="1000" height="600"></canvas>
                    <div class="canvas-info">
                        <span>Sheet: <span id="sheet-dimensions">22" × 72"</span></span>
                        <span>Scale: <span id="canvas-scale">1:10</span></span>
                        <span>Print Size: <span id="print-size">22" × 72"</span></span>
                    </div>
                </div>

                <div id="status" class="status hidden"></div>
            </div>
        </div>
    </div>

    <script>
        class ProfessionalDTFBuilder {
            constructor() {
                this.canvas = document.getElementById('gang-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.images = [];
                this.sheetSize = '22x72';
                this.dpi = 300;
                this.spacing = 3; // mm
                this.bleed = 1; // mm
                this.autoRotate = true;
                this.maintainAspect = true;
                this.addMargins = true;
                
                this.init();
            }

            init() {
                this.setupEvents();
                this.updateCanvas();
                this.updateStats();
            }

            setupEvents() {
                // File upload
                const uploadZone = document.getElementById('upload-zone');
                const fileInput = document.getElementById('file-input');

                uploadZone.onclick = () => fileInput.click();
                uploadZone.ondragover = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#3498db';
                };
                uploadZone.ondragleave = () => {
                    uploadZone.style.borderColor = '#bdc3c7';
                };
                uploadZone.ondrop = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#bdc3c7';
                    this.handleFiles(e.dataTransfer.files);
                };
                fileInput.onchange = (e) => this.handleFiles(e.target.files);

                // Settings
                document.getElementById('sheet-size').onchange = (e) => {
                    this.sheetSize = e.target.value;
                    this.updateCanvas();
                    this.updateStats();
                };

                document.getElementById('dpi').onchange = (e) => {
                    this.dpi = parseInt(e.target.value);
                    this.updateStats();
                };

                document.getElementById('spacing').oninput = (e) => {
                    this.spacing = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('bleed').oninput = (e) => {
                    this.bleed = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('auto-rotate').onchange = (e) => {
                    this.autoRotate = e.target.checked;
                };

                document.getElementById('maintain-aspect').onchange = (e) => {
                    this.maintainAspect = e.target.checked;
                };

                document.getElementById('add-margins').onchange = (e) => {
                    this.addMargins = e.target.checked;
                };

                // Actions
                document.getElementById('auto-nest-btn').onclick = () => this.autoNest();
                document.getElementById('optimize-btn').onclick = () => this.optimizeLayout();
                document.getElementById('download-btn').onclick = () => this.downloadGangSheet();
            }

            handleFiles(files) {
                Array.from(files).forEach(file => {
                    if (file.type.startsWith('image/') || file.type === 'application/pdf') {
                        if (file.size > 50 * 1024 * 1024) {
                            this.showStatus('File too large: ' + file.name, 'error');
                            return;
                        }

                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = new Image();
                            img.onload = () => {
                                this.addImage({
                                    id: Date.now() + Math.random(),
                                    name: file.name,
                                    image: img,
                                    width: img.width,
                                    height: img.height,
                                    quantity: 1,
                                    src: e.target.result
                                });
                            };
                            img.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            addImage(imageData) {
                this.images.push(imageData);
                this.updateImageList();
                this.enableButtons();
                this.updateStats();
                this.showStatus(`Added: ${imageData.name}`, 'success');
            }

            updateImageList() {
                const list = document.getElementById('image-list');
                const uploadZone = document.getElementById('upload-zone');
                
                if (this.images.length === 0) {
                    list.classList.add('hidden');
                    uploadZone.classList.remove('has-files');
                    return;
                }

                uploadZone.classList.add('has-files');
                list.classList.remove('hidden');
                
                list.innerHTML = this.images.map(img => `
                    <div class="image-item">
                        <img src="${img.src}" class="image-thumb" alt="${img.name}">
                        <div class="image-info">
                            <div class="image-name">${img.name}</div>
                            <div class="image-size">${img.width} × ${img.height}px</div>
                        </div>
                        <input type="number" class="quantity-input" value="${img.quantity}" 
                               min="1" max="100" onchange="dtfBuilder.updateQuantity('${img.id}', this.value)">
                    </div>
                `).join('');
            }

            updateQuantity(id, quantity) {
                const image = this.images.find(img => img.id == id);
                if (image) {
                    image.quantity = Math.max(1, parseInt(quantity) || 1);
                    this.updateStats();
                }
            }

            enableButtons() {
                document.getElementById('auto-nest-btn').disabled = false;
                document.getElementById('optimize-btn').disabled = false;
            }

            autoNest() {
                if (this.images.length === 0) {
                    this.showStatus('Please add images first', 'error');
                    return;
                }

                this.showStatus('Auto-nesting images...', 'info');
                
                // Professional auto-nesting algorithm
                const layout = this.calculateOptimalLayout();
                this.renderLayout(layout);
                
                document.getElementById('download-btn').classList.remove('hidden');
                this.showStatus(`Auto-nested ${layout.totalCopies} images with ${layout.efficiency}% efficiency`, 'success');
            }

            calculateOptimalLayout() {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;
                
                // Convert sheet dimensions to pixels (for display)
                const scale = Math.min(canvasWidth / (sheetWidth * 25.4), canvasHeight / (sheetHeight * 25.4));
                
                const positions = [];
                let currentX = this.addMargins ? this.mmToPixels(5, scale) : 0;
                let currentY = this.addMargins ? this.mmToPixels(5, scale) : 0;
                let rowHeight = 0;
                let totalCopies = 0;

                this.images.forEach(imageData => {
                    for (let i = 0; i < imageData.quantity; i++) {
                        const imgWidth = imageData.width * scale * 0.1; // Scale down for display
                        const imgHeight = imageData.height * scale * 0.1;
                        const spacing = this.mmToPixels(this.spacing, scale);

                        // Check if image fits in current row
                        if (currentX + imgWidth > canvasWidth - (this.addMargins ? this.mmToPixels(5, scale) : 0)) {
                            // Move to next row
                            currentX = this.addMargins ? this.mmToPixels(5, scale) : 0;
                            currentY += rowHeight + spacing;
                            rowHeight = 0;
                        }

                        // Check if image fits in sheet height
                        if (currentY + imgHeight <= canvasHeight - (this.addMargins ? this.mmToPixels(5, scale) : 0)) {
                            positions.push({
                                imageData,
                                x: currentX,
                                y: currentY,
                                width: imgWidth,
                                height: imgHeight
                            });

                            currentX += imgWidth + spacing;
                            rowHeight = Math.max(rowHeight, imgHeight);
                            totalCopies++;
                        }
                    }
                });

                const usedArea = positions.reduce((sum, pos) => sum + (pos.width * pos.height), 0);
                const totalArea = canvasWidth * canvasHeight;
                const efficiency = Math.round((usedArea / totalArea) * 100);

                return { positions, totalCopies, efficiency };
            }

            renderLayout(layout) {
                // Clear canvas
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw sheet outline
                this.ctx.strokeStyle = '#34495e';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw margins if enabled
                if (this.addMargins) {
                    const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                    const scale = Math.min(this.canvas.width / (sheetWidth * 25.4), this.canvas.height / (sheetHeight * 25.4));
                    const margin = this.mmToPixels(5, scale);
                    
                    this.ctx.strokeStyle = '#e74c3c';
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.strokeRect(margin, margin, this.canvas.width - margin * 2, this.canvas.height - margin * 2);
                    this.ctx.setLineDash([]);
                }

                // Draw images
                layout.positions.forEach(pos => {
                    this.ctx.drawImage(pos.imageData.image, pos.x, pos.y, pos.width, pos.height);
                    
                    // Draw border around each image
                    this.ctx.strokeStyle = '#3498db';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(pos.x, pos.y, pos.width, pos.height);
                });

                this.updateStats(layout);
            }

            mmToPixels(mm, scale) {
                return (mm / 25.4) * this.dpi * scale;
            }

            updateCanvas() {
                if (this.images.length > 0) {
                    this.autoNest();
                } else {
                    this.drawEmptySheet();
                }
                this.updateSheetInfo();
            }

            drawEmptySheet() {
                this.ctx.fillStyle = '#f8f9fa';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                this.ctx.strokeStyle = '#bdc3c7';
                this.ctx.setLineDash([10, 10]);
                this.ctx.strokeRect(20, 20, this.canvas.width - 40, this.canvas.height - 40);
                
                this.ctx.fillStyle = '#7f8c8d';
                this.ctx.font = '18px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('Professional DTF Gang Sheet', this.canvas.width / 2, this.canvas.height / 2 - 10);
                this.ctx.fillText('Upload images to begin auto-nesting', this.canvas.width / 2, this.canvas.height / 2 + 20);
            }

            updateSheetInfo() {
                const [width, height] = this.sheetSize.split('x');
                document.getElementById('sheet-dimensions').textContent = `${width}" × ${height}"`;
                document.getElementById('print-size').textContent = `${width}" × ${height}"`;
            }

            updateStats(layout = null) {
                const totalImages = this.images.length;
                const totalCopies = this.images.reduce((sum, img) => sum + img.quantity, 0);
                const efficiency = layout ? layout.efficiency : 0;

                document.getElementById('total-images').textContent = totalImages;
                document.getElementById('total-copies').textContent = totalCopies;
                document.getElementById('efficiency').textContent = efficiency + '%';
            }

            optimizeLayout() {
                this.showStatus('Optimizing layout for maximum efficiency...', 'info');
                // Advanced optimization would go here
                this.autoNest();
            }

            downloadGangSheet() {
                const link = document.createElement('a');
                link.download = `dtf-gang-sheet-${this.sheetSize}-${this.dpi}dpi.png`;
                link.href = this.canvas.toDataURL('image/png', 1.0);
                link.click();
                
                this.showStatus('Gang sheet downloaded successfully!', 'success');
            }

            showStatus(message, type) {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.classList.remove('hidden');
                
                setTimeout(() => status.classList.add('hidden'), 4000);
            }
        }

        // Initialize
        const dtfBuilder = new ProfessionalDTFBuilder();
        window.dtfBuilder = dtfBuilder; // For quantity updates
    </script>
</body>
</html>
