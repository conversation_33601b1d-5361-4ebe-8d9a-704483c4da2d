-- DTF Gang Builder MySQL Setup Script
-- Run this script to create the necessary database and user

-- Create the database
CREATE DATABASE IF NOT EXISTS dtf_gang_builder CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create the DTF user with password authentication
CREATE USER IF NOT EXISTS 'dtf_user'@'localhost' IDENTIFIED BY 'dtf_password_2024';

-- Grant all privileges on the DTF database to the user
GRANT ALL PRIVILEGES ON dtf_gang_builder.* TO 'dtf_user'@'localhost';

-- Refresh privileges
FLUSH PRIVILEGES;

-- Show the created database
SHOW DATABASES LIKE 'dtf_gang_builder';

-- Show the created user
SELECT user, host, plugin FROM mysql.user WHERE user='dtf_user';

-- Test the database
USE dtf_gang_builder;

-- Create a test table to verify permissions
CREATE TABLE IF NOT EXISTS dtf_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert test data
INSERT INTO dtf_test (message) VALUES ('DTF Gang Builder setup successful!');

-- Show the test data
SELECT * FROM dtf_test;

-- Clean up test table
DROP TABLE dtf_test;

-- Show success message
SELECT 'DTF Gang Builder MySQL setup completed successfully!' AS status;
