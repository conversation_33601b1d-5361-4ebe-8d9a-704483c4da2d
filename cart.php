<?php
/**
 * CYPTSHOP Shopping Cart Page
 * Tasks *******.1 - *******.5: Cart System Implementation
 */

define('BASE_PATH', __DIR__ . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session
session_start();

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Get products data for cart items
$products = getJsonData(PRODUCTS_JSON);
$cartItems = [];
$cartTotal = 0;
$cartSubtotal = 0;
$shippingCost = 0;
$taxRate = 0.08; // 8% tax

// Build cart items with product details
foreach ($_SESSION['cart'] as $cartItemId => $cartItem) {
    $product = null;
    foreach ($products as $p) {
        if ($p['id'] === $cartItem['product_id']) {
            $product = $p;
            break;
        }
    }

    if ($product) {
        $price = $product['sale_price'] ?? $product['price'];
        $itemTotal = $price * $cartItem['quantity'];

        $cartItems[] = [
            'cart_item_id' => $cartItemId,
            'product' => $product,
            'quantity' => $cartItem['quantity'],
            'size' => $cartItem['size'] ?? null,
            'color' => $cartItem['color'] ?? null,
            'price' => $price,
            'total' => $itemTotal
        ];

        $cartSubtotal += $itemTotal;
    }
}

// Calculate shipping (free over $50)
$shippingCost = $cartSubtotal >= 50 ? 0 : 9.99;

// Calculate tax
$taxAmount = $cartSubtotal * $taxRate;

// Calculate total
$cartTotal = $cartSubtotal + $shippingCost + $taxAmount;

// Page variables
$pageTitle = 'Shopping Cart - CYPTSHOP';
$pageDescription = 'Review your cart and proceed to checkout';
$bodyClass = 'cart-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Modern Studio Sub-Hero Section -->
<section class="sub-hero cart-hero">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sub-hero-content">
                    <div class="sub-hero-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h1 class="sub-hero-title">Shopping Cart</h1>
                    <p class="sub-hero-description">
                        Review your selected items and proceed to secure checkout.
                        Your custom designs are just one step away from becoming reality.
                    </p>
                    <div class="sub-hero-breadcrumb">
                        <a href="<?php echo SITE_URL; ?>/">Home</a>
                        <span class="separator">/</span>
                        <a href="<?php echo SITE_URL; ?>/shop/">Shop</a>
                        <span class="separator">/</span>
                        <span class="current">Cart</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Cart Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <?php if (!empty($cartItems)): ?>
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="cart-items">
                        <div class="card bg-dark-grey-1 border-dark-grey-3">
                            <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                                <h5 class="mb-0 text-cyan">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    Cart Items (<?php echo count($cartItems); ?>)
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <?php foreach ($cartItems as $item): ?>
                                    <div class="cart-item border-bottom border-dark-grey-3 p-4"
                                         data-cart-item-id="<?php echo $item['cart_item_id']; ?>">
                                        <div class="row align-items-center">
                                            <!-- Product Image -->
                                            <div class="col-md-2">
                                                <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $item['product']['image'] ?? 'placeholder.jpg'; ?>"
                                                     class="img-fluid rounded-3"
                                                     alt="<?php echo htmlspecialchars($item['product']['name']); ?>"
                                                     style="height: 80px; object-fit: cover;">
                                            </div>

                                            <!-- Product Details -->
                                            <div class="col-md-4">
                                                <h6 class="text-white mb-1">
                                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $item['product']['id']; ?>"
                                                       class="text-decoration-none text-white">
                                                        <?php echo htmlspecialchars($item['product']['name']); ?>
                                                    </a>
                                                </h6>
                                                <div class="text-off-white small">
                                                    <?php if ($item['size']): ?>
                                                        <span class="me-3">Size: <strong><?php echo htmlspecialchars($item['size']); ?></strong></span>
                                                    <?php endif; ?>
                                                    <?php if ($item['color']): ?>
                                                        <span>Color: <strong><?php echo htmlspecialchars($item['color']); ?></strong></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <!-- Price -->
                                            <div class="col-md-2 text-center">
                                                <span class="text-cyan fw-bold">$<?php echo number_format($item['price'], 2); ?></span>
                                            </div>

                                            <!-- Quantity Controls -->
                                            <div class="col-md-2">
                                                <div class="quantity-controls d-flex align-items-center justify-content-center">
                                                    <button type="button" class="btn btn-outline-cyan btn-sm quantity-minus">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <input type="number" class="form-control quantity-input mx-2 text-center"
                                                           value="<?php echo $item['quantity']; ?>" min="1" max="99"
                                                           style="width: 60px;">
                                                    <button type="button" class="btn btn-outline-cyan btn-sm quantity-plus">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Item Total -->
                                            <div class="col-md-1 text-center">
                                                <span class="text-white fw-bold item-total">$<?php echo number_format($item['total'], 2); ?></span>
                                            </div>

                                            <!-- Remove Button -->
                                            <div class="col-md-1 text-center">
                                                <button class="btn btn-outline-danger btn-sm remove-cart-item"
                                                        title="Remove item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Continue Shopping -->
                        <div class="mt-4">
                            <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-outline-cyan">
                                <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <div class="cart-summary">
                        <div class="card bg-dark-grey-1 border-dark-grey-3">
                            <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                                <h5 class="mb-0 text-magenta">
                                    <i class="fas fa-calculator me-2"></i>
                                    Order Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Subtotal -->
                                <div class="d-flex justify-content-between mb-3">
                                    <span class="text-off-white">Subtotal:</span>
                                    <span class="text-white cart-subtotal">$<?php echo number_format($cartSubtotal, 2); ?></span>
                                </div>

                                <!-- Shipping -->
                                <div class="d-flex justify-content-between mb-3">
                                    <span class="text-off-white">Shipping:</span>
                                    <span class="text-white">
                                        <?php if ($shippingCost > 0): ?>
                                            $<?php echo number_format($shippingCost, 2); ?>
                                        <?php else: ?>
                                            <span class="text-success">FREE</span>
                                        <?php endif; ?>
                                    </span>
                                </div>

                                <!-- Tax -->
                                <div class="d-flex justify-content-between mb-3">
                                    <span class="text-off-white">Tax (<?php echo ($taxRate * 100); ?>%):</span>
                                    <span class="text-white">$<?php echo number_format($taxAmount, 2); ?></span>
                                </div>

                                <hr class="border-dark-grey-3">

                                <!-- Total -->
                                <div class="d-flex justify-content-between mb-4">
                                    <span class="text-cyan fw-bold h5">Total:</span>
                                    <span class="text-cyan fw-bold h5 cart-total">$<?php echo number_format($cartTotal, 2); ?></span>
                                </div>

                                <!-- Shipping Notice -->
                                <?php if ($cartSubtotal < 50): ?>
                                    <div class="alert alert-info bg-dark-grey-2 border-cyan text-cyan">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Add $<?php echo number_format(50 - $cartSubtotal, 2); ?> more for FREE shipping!
                                    </div>
                                <?php endif; ?>

                                <!-- Checkout Button -->
                                <div class="d-grid">
                                    <a href="<?php echo SITE_URL; ?>/checkout.php" class="btn btn-cyan btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                    </a>
                                </div>

                                <!-- PayPal Express -->
                                <div class="mt-3">
                                    <div id="paypal-button-container"></div>
                                </div>

                                <!-- Security Notice -->
                                <div class="mt-3 text-center">
                                    <small class="text-off-white">
                                        <i class="fas fa-lock me-1"></i>
                                        Secure checkout with SSL encryption
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Coupon Code -->
                        <div class="card bg-dark-grey-1 border-dark-grey-3 mt-4">
                            <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                                <h6 class="mb-0 text-yellow">
                                    <i class="fas fa-tag me-2"></i>
                                    Coupon Code
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="couponForm">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="coupon_code"
                                               placeholder="Enter coupon code">
                                        <button class="btn btn-yellow text-black" type="submit">
                                            Apply
                                        </button>
                                    </div>
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- Empty Cart -->
            <div class="row">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-shopping-cart fa-5x text-dark-grey-3 mb-4"></i>
                    <h3 class="text-white mb-3">Your cart is empty</h3>
                    <p class="text-off-white mb-4">
                        Looks like you haven't added any items to your cart yet.
                    </p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="<?php echo SITE_URL; ?>/shop.php" class="btn btn-cyan btn-lg">
                            <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                        </a>
                        <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-outline-magenta btn-lg">
                            <i class="fas fa-cogs me-2"></i>View Services
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- CSRF Token for AJAX -->
<meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

<script>
// Update cart totals after quantity changes
function updateCartTotals() {
    fetch('<?php echo SITE_URL; ?>/cart/totals.php')
        .then(response => response.json())
        .then(data => {
            document.querySelector('.cart-subtotal').textContent = '$' + data.subtotal;
            document.querySelector('.cart-total').textContent = '$' + data.total;
            updateCartCounter();
        });
}

// Coupon form handling
document.getElementById('couponForm')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('<?php echo SITE_URL; ?>/cart/coupon.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Coupon applied successfully!', 'success');
            location.reload(); // Reload to show updated prices
        } else {
            showNotification(data.message || 'Invalid coupon code', 'error');
        }
    })
    .catch(error => {
        showNotification('Error applying coupon', 'error');
    });
});

// PayPal integration (if PayPal SDK is loaded)
if (typeof paypal !== 'undefined') {
    paypal.Buttons({
        createOrder: function(data, actions) {
            return actions.order.create({
                purchase_units: [{
                    amount: {
                        value: '<?php echo number_format($cartTotal, 2, '.', ''); ?>'
                    }
                }]
            });
        },
        onApprove: function(data, actions) {
            return actions.order.capture().then(function(details) {
                // Redirect to success page
                window.location.href = '<?php echo SITE_URL; ?>/checkout/success.php?order_id=' + details.id;
            });
        },
        onError: function(err) {
            showNotification('PayPal payment error', 'error');
        }
    }).render('#paypal-button-container');
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
