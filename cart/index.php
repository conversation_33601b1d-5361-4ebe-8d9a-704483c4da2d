<?php
/**
 * CYPTSHOP Shopping Cart Page
 * Tasks *******.1 - *******.5: Cart System Implementation
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// This page will be populated by JavaScript from localStorage
// The cart data is managed by the frontend cart system
$cartItems = [];
$cartTotal = 0;
$cartSubtotal = 0;
$shippingCost = 0;
$taxRate = 0.08; // 8% tax

// Page variables
$pageTitle = 'Shopping Cart - CYPTSHOP';
$pageDescription = 'Review your cart and proceed to checkout';
$bodyClass = 'cart-page';

include BASE_PATH . 'includes/header.php';
?>

<!-- Cart Hero Section -->
<section class="py-5 bg-black">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-cyan mb-3">
                    <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                </h1>
                <p class="text-off-white lead">
                    Review your items and proceed to checkout
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Cart Content -->
<section class="py-5 bg-dark-grey-1">
    <div class="container">
        <div class="row">
            <!-- Cart Items -->
            <div class="col-lg-8">
                <div class="card bg-dark-grey-2 border-cyan">
                    <div class="card-header bg-dark-grey-3 border-bottom border-cyan">
                        <h5 class="text-white mb-0">
                            <i class="fas fa-list me-2"></i>Cart Items
                            <span class="badge bg-magenta text-black ms-2" id="cartItemCount">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <!-- Empty Cart Message -->
                        <div id="emptyCartView" class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-4x text-dark-grey-3 mb-4"></i>
                            <h4 class="text-white mb-3">Your cart is empty</h4>
                            <p class="text-off-white mb-4">
                                Looks like you haven't added any items to your cart yet.<br>
                                Start shopping to fill it up!
                            </p>
                            <div class="d-flex gap-3 justify-content-center">
                                <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-cyan">
                                    <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                                </a>
                                <a href="<?php echo SITE_URL; ?>/" class="btn btn-outline-magenta">
                                    <i class="fas fa-home me-2"></i>Back to Home
                                </a>
                            </div>
                        </div>

                        <!-- Cart Items Table -->
                        <div id="cartItemsView" style="display: none;">
                            <div class="table-responsive">
                                <table class="table table-dark table-hover mb-0">
                                    <thead class="bg-dark-grey-3">
                                        <tr>
                                            <th class="text-cyan">Product</th>
                                            <th class="text-cyan text-center">Price</th>
                                            <th class="text-cyan text-center">Quantity</th>
                                            <th class="text-cyan text-center">Total</th>
                                            <th class="text-cyan text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cartTableBody">
                                        <!-- Cart items will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cart Actions -->
                <div class="mt-4">
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="<?php echo SITE_URL; ?>/shop/" class="btn btn-outline-cyan">
                            <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                        </a>
                        <button class="btn btn-outline-danger" onclick="clearCart()">
                            <i class="fas fa-trash me-2"></i>Clear Cart
                        </button>
                        <button class="btn btn-outline-yellow" onclick="updateCart()">
                            <i class="fas fa-sync me-2"></i>Update Cart
                        </button>
                    </div>
                </div>
            <!-- Cart Summary -->
            <div class="col-lg-4">
                <div class="card bg-dark-grey-2 border-magenta sticky-top" style="top: 100px;">
                    <div class="card-header bg-dark-grey-3 border-bottom border-magenta">
                        <h5 class="text-magenta mb-0">
                            <i class="fas fa-calculator me-2"></i>Order Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="cart-summary-details">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-off-white">Subtotal:</span>
                                <span class="text-white" id="cartSubtotalView">$0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-off-white">Shipping:</span>
                                <span class="text-yellow" id="cartShippingView">FREE</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-off-white">Tax:</span>
                                <span class="text-white" id="cartTaxView">$0.00</span>
                            </div>
                            <hr class="border-dark-grey-3">
                            <div class="d-flex justify-content-between mb-4">
                                <span class="text-white h5 mb-0">Total:</span>
                                <span class="text-cyan h4 mb-0" id="cartTotalView">$0.00</span>
                            </div>
                        </div>

                        <!-- Coupon Code -->
                        <div class="coupon-section mb-4">
                            <label class="form-label text-white">
                                <i class="fas fa-tag me-2"></i>Coupon Code
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control bg-dark-grey-3 border-dark-grey-3 text-white"
                                       id="couponCodeInput" placeholder="Enter coupon code">
                                <button class="btn btn-yellow text-black" onclick="applyCoupon()">
                                    Apply
                                </button>
                            </div>
                            <div id="couponMessage" class="mt-2"></div>
                        </div>

                        <!-- Checkout Button -->
                        <button class="btn btn-magenta w-100 btn-lg mb-3" onclick="proceedToCheckout()" id="checkoutBtn">
                            <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                        </button>

                        <!-- Security Info -->
                        <div class="security-info text-center">
                            <small class="text-off-white">
                                <i class="fas fa-lock me-1"></i>
                                Secure checkout with SSL encryption
                            </small>
                            <div class="payment-methods mt-2">
                                <i class="fab fa-cc-visa text-cyan me-2"></i>
                                <i class="fab fa-cc-mastercard text-magenta me-2"></i>
                                <i class="fab fa-cc-paypal text-yellow me-2"></i>
                                <i class="fab fa-cc-apple-pay text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
// Cart view specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    loadCartView();
});

function loadCartView() {
    // Get cart from localStorage or global cart variable
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');

    if (cartData.length === 0) {
        showEmptyCart();
    } else {
        showCartItems(cartData);
    }
}

function showEmptyCart() {
    document.getElementById('emptyCartView').style.display = 'block';
    document.getElementById('cartItemsView').style.display = 'none';
    document.getElementById('cartItemCount').textContent = '0';
    document.getElementById('checkoutBtn').disabled = true;
}

function showCartItems(cartData) {
    document.getElementById('emptyCartView').style.display = 'none';
    document.getElementById('cartItemsView').style.display = 'block';

    const tbody = document.getElementById('cartTableBody');
    const itemCount = cartData.reduce((total, item) => total + item.quantity, 0);

    document.getElementById('cartItemCount').textContent = itemCount;

    tbody.innerHTML = cartData.map(item => `
        <tr data-product-id="${item.id}">
            <td>
                <div class="d-flex align-items-center">
                    <img src="/assets/images/products/${item.image || 'placeholder.jpg'}"
                         alt="${item.name}" class="me-3" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
                    <div>
                        <h6 class="text-white mb-1">${item.name}</h6>
                        <small class="text-off-white">SKU: ${item.id}</small>
                    </div>
                </div>
            </td>
            <td class="text-center text-cyan">$${item.price.toFixed(2)}</td>
            <td class="text-center">
                <div class="quantity-controls d-flex justify-content-center">
                    <button class="btn btn-sm btn-outline-cyan" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                    <input type="number" class="form-control form-control-sm mx-2 text-center bg-dark-grey-3 border-dark-grey-3 text-white"
                           style="width: 60px;" value="${item.quantity}" min="1"
                           onchange="updateQuantity('${item.id}', this.value)">
                    <button class="btn btn-sm btn-outline-cyan" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                </div>
            </td>
            <td class="text-center text-yellow fw-bold">$${(item.price * item.quantity).toFixed(2)}</td>
            <td class="text-center">
                <button class="btn btn-sm btn-outline-danger" onclick="removeFromCartView('${item.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');

    updateCartSummary(cartData);
    document.getElementById('checkoutBtn').disabled = false;
}

function updateCartSummary(cartData) {
    const subtotal = cartData.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = subtotal > 75 ? 0 : 8.99;
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + shipping + tax;

    document.getElementById('cartSubtotalView').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('cartShippingView').textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
    document.getElementById('cartTaxView').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('cartTotalView').textContent = `$${total.toFixed(2)}`;
}

function updateQuantity(productId, newQuantity) {
    if (newQuantity < 1) {
        removeFromCartView(productId);
        return;
    }

    // Update global cart
    if (window.cart) {
        const item = window.cart.find(item => item.id === productId);
        if (item) {
            item.quantity = parseInt(newQuantity);
            window.updateCartDisplay();
        }
    }

    // Update localStorage
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');
    const item = cartData.find(item => item.id === productId);
    if (item) {
        item.quantity = parseInt(newQuantity);
        localStorage.setItem('cyptshop_cart', JSON.stringify(cartData));
    }

    loadCartView();
}

function removeFromCartView(productId) {
    // Update global cart
    if (window.cart) {
        window.cart = window.cart.filter(item => item.id !== productId);
        window.updateCartDisplay();
    }

    // Update localStorage
    const cartData = JSON.parse(localStorage.getItem('cyptshop_cart') || '[]');
    const updatedCart = cartData.filter(item => item.id !== productId);
    localStorage.setItem('cyptshop_cart', JSON.stringify(updatedCart));

    loadCartView();
}

function updateCart() {
    loadCartView();
    showNotification('Cart updated successfully!', 'success');
}

function applyCoupon() {
    const couponCode = document.getElementById('couponCodeInput').value.trim();
    const messageDiv = document.getElementById('couponMessage');

    if (!couponCode) {
        messageDiv.innerHTML = '<small class="text-danger">Please enter a coupon code</small>';
        return;
    }

    // Mock coupon validation
    const validCoupons = {
        'SAVE10': { discount: 10, type: 'percentage' },
        'DETROIT': { discount: 15, type: 'percentage' },
        'NEWBIE': { discount: 5, type: 'fixed' }
    };

    if (validCoupons[couponCode.toUpperCase()]) {
        messageDiv.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>Coupon applied successfully!</small>';
        // Apply discount logic here
    } else {
        messageDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>Invalid coupon code</small>';
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000;';
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

<?php include BASE_PATH . 'includes/footer.php'; ?>
