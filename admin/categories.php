<?php
/**
 * CYPTSHOP Category Management System
 * Complete hierarchical category management with drag-and-drop
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get database connection
$pdo = getDatabaseConnection();

// Initialize categories in database if empty
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $categoryCount = $stmt->fetchColumn();

    if ($categoryCount == 0) {
        // Insert default categories
        $defaultCategories = [
            [
                'name' => 'T-Shirts',
                'slug' => 'tshirts',
                'description' => 'Custom designed t-shirts with Detroit style',
                'image' => 'category-tshirts.jpg',
                'parent_id' => null,
                'sort_order' => 1,
                'status' => 'active',
                'seo_title' => 'Custom T-Shirts - Detroit Style',
                'seo_description' => 'Browse our collection of custom Detroit-style t-shirts'
            ],
            [
                'name' => 'Hoodies',
                'slug' => 'hoodies',
                'description' => 'Comfortable hoodies with urban Detroit designs',
                'image' => 'category-hoodies.jpg',
                'parent_id' => null,
                'sort_order' => 2,
                'status' => 'active',
                'seo_title' => 'Custom Hoodies - Detroit Urban Style',
                'seo_description' => 'Shop our Detroit-inspired custom hoodies'
            ],
            [
                'name' => 'Accessories',
                'slug' => 'accessories',
                'description' => 'Custom accessories and merchandise',
                'image' => 'category-accessories.jpg',
                'parent_id' => null,
                'sort_order' => 3,
                'status' => 'active',
                'seo_title' => 'Custom Accessories - Detroit Style',
                'seo_description' => 'Complete your look with Detroit-style accessories'
            ]
        ];

        foreach ($defaultCategories as $category) {
            $stmt = $pdo->prepare("
                INSERT INTO categories (name, slug, description, image, parent_id, sort_order, status, seo_title, seo_description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $category['name'],
                $category['slug'],
                $category['description'],
                $category['image'],
                $category['parent_id'],
                $category['sort_order'],
                $category['status'],
                $category['seo_title'],
                $category['seo_description']
            ]);
        }
    }
} catch (Exception $e) {
    error_log('Category initialization error: ' . $e->getMessage());
}

// Get categories from database
$categories = getCategories();
$products = getProducts();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'add':
                $name = trim($_POST['name'] ?? '');
                $slug = strtolower(trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $name)));
                $description = trim($_POST['description'] ?? '');
                $image = trim($_POST['image'] ?? '');
                $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
                $sort_order = intval($_POST['sort_order'] ?? count($categories) + 1);
                $status = isset($_POST['active']) ? 'active' : 'inactive';
                $seo_title = trim($_POST['seo_title'] ?? '');
                $seo_description = trim($_POST['seo_description'] ?? '');

                if (empty($name)) {
                    $error = 'Category name is required.';
                } else {
                    try {
                        $stmt = $pdo->prepare("
                            INSERT INTO categories (name, slug, description, image, parent_id, sort_order, status, seo_title, seo_description)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");

                        if ($stmt->execute([$name, $slug, $description, $image, $parent_id, $sort_order, $status, $seo_title, $seo_description])) {
                            $success = 'Category added successfully!';
                            $categories = getCategories(); // Refresh categories
                        } else {
                            $error = 'Failed to save category.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'edit':
                $categoryId = intval($_POST['category_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $slug = strtolower(trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $name)));
                $description = trim($_POST['description'] ?? '');
                $image = trim($_POST['image'] ?? '');
                $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
                $sort_order = intval($_POST['sort_order'] ?? 1);
                $status = isset($_POST['active']) ? 'active' : 'inactive';
                $seo_title = trim($_POST['seo_title'] ?? '');
                $seo_description = trim($_POST['seo_description'] ?? '');

                if (empty($name)) {
                    $error = 'Category name is required.';
                } else {
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE categories
                            SET name = ?, slug = ?, description = ?, image = ?, parent_id = ?,
                                sort_order = ?, status = ?, seo_title = ?, seo_description = ?, updated_at = NOW()
                            WHERE id = ?
                        ");

                        if ($stmt->execute([$name, $slug, $description, $image, $parent_id, $sort_order, $status, $seo_title, $seo_description, $categoryId])) {
                            $success = 'Category updated successfully!';
                            $categories = getCategories(); // Refresh categories
                        } else {
                            $error = 'Failed to update category.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete':
                $categoryId = intval($_POST['category_id'] ?? 0);

                try {
                    $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");

                    if ($stmt->execute([$categoryId])) {
                        $success = 'Category deleted successfully!';
                        $categories = getCategories(); // Refresh categories
                    } else {
                        $error = 'Failed to delete category.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'reorder':
                $orderData = json_decode($_POST['order_data'] ?? '[]', true);
                foreach ($categories as &$category) {
                    foreach ($orderData as $order) {
                        if ($category['id'] === $order['id']) {
                            $category['sort_order'] = $order['order'];
                            $category['parent_id'] = $order['parent'] ?? null;
                            break;
                        }
                    }
                }

                if (saveJsonData($categoriesFile, $categories)) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false]);
                }
                exit;

            case 'bulk_action':
                $selectedCategories = $_POST['selected_categories'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';

                if (empty($selectedCategories) || empty($bulkAction)) {
                    $error = 'Please select categories and an action.';
                } else {
                    $updatedCount = 0;

                    foreach ($categories as &$category) {
                        if (in_array($category['id'], $selectedCategories)) {
                            switch ($bulkAction) {
                                case 'activate':
                                    $category['active'] = true;
                                    $updatedCount++;
                                    break;
                                case 'deactivate':
                                    $category['active'] = false;
                                    $updatedCount++;
                                    break;
                                case 'delete':
                                    // Mark for deletion
                                    $category['_delete'] = true;
                                    $updatedCount++;
                                    break;
                            }
                            $category['updated_at'] = date('Y-m-d H:i:s');
                        }
                    }

                    // Remove deleted categories
                    if ($bulkAction === 'delete') {
                        $categories = array_filter($categories, function($cat) {
                            return !isset($cat['_delete']);
                        });
                    }

                    if (saveJsonData($categoriesFile, $categories)) {
                        $success = "Bulk action completed successfully! {$updatedCount} categories updated.";
                    } else {
                        $error = 'Failed to perform bulk action.';
                    }
                }
                break;
        }
    }
}

// Calculate category statistics
function getCategoryStats($categoryId, $products) {
    $count = 0;
    foreach ($products as $product) {
        if (isset($product['category_id']) && $product['category_id'] == $categoryId) {
            $count++;
        } elseif (isset($product['category']) && $product['category'] === $categoryId) {
            $count++;
        }
    }
    return $count;
}

// Build category tree
function buildCategoryTree($categories, $parentId = null) {
    $tree = [];
    foreach ($categories as $category) {
        if ($category['parent_id'] == $parentId) {
            $category['children'] = buildCategoryTree($categories, $category['id']);
            $tree[] = $category;
        }
    }

    // Sort by sort_order
    usort($tree, function($a, $b) {
        return $a['sort_order'] - $b['sort_order'];
    });

    return $tree;
}

// Apply filters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');

$filteredCategories = $categories;

// Apply search filter
if (!empty($search)) {
    $filteredCategories = array_filter($filteredCategories, function($category) use ($search) {
        return stripos($category['name'], $search) !== false ||
               stripos($category['description'], $search) !== false;
    });
}

// Apply status filter
switch ($filter) {
    case 'active':
        $filteredCategories = array_filter($filteredCategories, function($cat) {
            return $cat['status'] === 'active';
        });
        break;
    case 'inactive':
        $filteredCategories = array_filter($filteredCategories, function($cat) {
            return $cat['status'] === 'inactive';
        });
        break;
    case 'parent':
        $filteredCategories = array_filter($filteredCategories, function($cat) {
            return $cat['parent_id'] === null || $cat['parent_id'] == 0;
        });
        break;
    case 'child':
        $filteredCategories = array_filter($filteredCategories, function($cat) {
            return $cat['parent_id'] !== null && $cat['parent_id'] > 0;
        });
        break;
}

$categoryTree = buildCategoryTree($filteredCategories);

$pageTitle = 'Category Management - Admin';
$bodyClass = 'admin-categories';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Category Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#categoryModal">
                        <i class="fas fa-plus me-2"></i>Add Category
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Filters and Search -->
            <div class="card bg-dark-grey-1 border-cyan mb-4">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-filter me-2"></i>Filter Categories
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="searchCategories" class="form-label text-white">Search</label>
                            <input type="text" class="form-control" id="searchCategories" name="search"
                                   placeholder="Search categories..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="filterStatus" class="form-label text-white">Filter</label>
                            <select class="form-select" id="filterStatus" name="filter">
                                <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Categories</option>
                                <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active Only</option>
                                <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>Inactive Only</option>
                                <option value="parent" <?php echo $filter === 'parent' ? 'selected' : ''; ?>>Parent Categories</option>
                                <option value="child" <?php echo $filter === 'child' ? 'selected' : ''; ?>>Child Categories</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label text-white">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-cyan">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="/admin/categories.php" class="btn btn-danger">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Operations -->
            <div class="card bg-dark-grey-1 border-yellow mb-4">
                <div class="card-header bg-dark-grey-2 border-yellow">
                    <h5 class="mb-0 text-yellow">
                        <i class="fas fa-tasks me-2"></i>Bulk Operations
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="bulkForm">
                        <input type="hidden" name="action" value="bulk_action">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label class="form-label text-white">Select Action</label>
                                <select class="form-select" name="bulk_action" required>
                                    <option value="">Choose action...</option>
                                    <option value="activate">Activate Selected</option>
                                    <option value="deactivate">Deactivate Selected</option>
                                    <option value="delete">Delete Selected</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-cyan" onclick="selectAllCategories()">
                                    <i class="fas fa-check-square me-2"></i>Select All
                                </button>
                                <button type="button" class="btn btn-danger" onclick="clearSelection()">
                                    <i class="fas fa-square me-2"></i>Clear All
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-yellow text-black" onclick="return confirmBulkAction()">
                                    <i class="fas fa-play me-2"></i>Execute Action
                                </button>
                                <span class="text-off-white ms-2">
                                    <span id="selectedCount">0</span> selected
                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Category Tree -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-sitemap me-2"></i>
                                Category Hierarchy
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="categoryTree" class="category-tree">
                                <?php echo renderCategoryTree($categoryTree, $products); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-chart-pie me-2"></i>
                                Category Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="category-stats">
                                <?php foreach ($categories as $category): ?>
                                    <div class="stat-item d-flex justify-content-between align-items-center mb-2">
                                        <span class="text-white"><?php echo htmlspecialchars($category['name']); ?></span>
                                        <span class="badge bg-cyan text-white">
                                            <?php echo getCategoryStats($category['slug'], $products); ?> products
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<?php
function renderCategoryTree($tree, $products, $level = 0) {
    $html = '<ul class="category-list" data-level="' . $level . '">';

    foreach ($tree as $category) {
        $productCount = getCategoryStats($category['id'], $products);
        $statusClass = ($category['status'] === 'active') ? 'text-success' : 'text-danger';
        $statusIcon = ($category['status'] === 'active') ? 'fa-check-circle' : 'fa-times-circle';

        $html .= '<li class="category-item" data-id="' . $category['id'] . '">';
        $html .= '<div class="category-content d-flex justify-content-between align-items-center p-3 mb-2 bg-dark-grey-2 rounded">';
        $html .= '<div class="category-info d-flex align-items-center">';
        $html .= '<div class="category-checkbox me-3">';
        $html .= '<input type="checkbox" class="form-check-input category-select" name="selected_categories[]" value="' . $category['id'] . '" onchange="updateSelectedCount()">';
        $html .= '</div>';
        $html .= '<div class="category-drag-handle me-3"><i class="fas fa-grip-vertical text-off-white"></i></div>';
        $html .= '<div class="category-details">';
        $html .= '<h6 class="text-white mb-1">' . htmlspecialchars($category['name']) . '</h6>';
        $html .= '<small class="text-light">' . htmlspecialchars($category['description']) . '</small>';
        $html .= '<div class="category-meta mt-1">';
        $html .= '<span class="badge bg-magenta text-black me-2">' . $productCount . ' products</span>';
        $html .= '<i class="fas ' . $statusIcon . ' ' . $statusClass . '"></i>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '<div class="category-actions">';
        $html .= '<button class="btn btn-sm btn-outline-cyan me-2" onclick="editCategory(\'' . $category['id'] . '\')">';
        $html .= '<i class="fas fa-edit"></i>';
        $html .= '</button>';
        $html .= '<button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(\'' . $category['id'] . '\')">';
        $html .= '<i class="fas fa-trash"></i>';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '</div>';

        if (!empty($category['children'])) {
            $html .= renderCategoryTree($category['children'], $products, $level + 1);
        }

        $html .= '</li>';
    }

    $html .= '</ul>';
    return $html;
}
?>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta">
                    <i class="fas fa-tag me-2"></i>
                    <span id="modalTitle">Add Category</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="categoryForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="category_id" id="categoryId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categoryName" class="form-label text-white">Category Name *</label>
                                <input type="text" class="form-control" id="categoryName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parentCategory" class="form-label text-white">Parent Category</label>
                                <select class="form-select" id="parentCategory" name="parent_id">
                                    <option value="">None (Top Level)</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo $cat['id']; ?>"><?php echo htmlspecialchars($cat['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label text-white">Description</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categoryImage" class="form-label text-white">Category Image</label>
                                <input type="text" class="form-control" id="categoryImage" name="image" placeholder="category-image.jpg">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sortOrder" class="form-label text-white">Sort Order</label>
                                <input type="number" class="form-control" id="sortOrder" name="sort_order" value="1" min="1">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="seoTitle" class="form-label text-white">SEO Title</label>
                        <input type="text" class="form-control" id="seoTitle" name="seo_title">
                    </div>

                    <div class="mb-3">
                        <label for="seoDescription" class="form-label text-white">SEO Description</label>
                        <textarea class="form-control" id="seoDescription" name="seo_description" rows="2"></textarea>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="categoryActive" name="active" checked>
                        <label class="form-check-label text-white" for="categoryActive">
                            Active Category
                        </label>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.category-tree {
    max-height: 600px;
    overflow-y: auto;
}

.category-list {
    list-style: none;
    padding-left: 0;
}

.category-list[data-level="1"] {
    padding-left: 30px;
}

.category-list[data-level="2"] {
    padding-left: 60px;
}

.category-item {
    margin-bottom: 10px;
}

.category-content {
    border: 1px solid var(--dark-grey-3);
    transition: all 0.3s ease;
}

.category-content:hover {
    border-color: var(--magenta);
    transform: translateY(-2px);
}

.category-drag-handle {
    cursor: move;
}

.category-drag-handle:hover {
    color: var(--magenta) !important;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    background-color: var(--dark-grey-3) !important;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Initialize sortable
document.addEventListener('DOMContentLoaded', function() {
    const categoryTree = document.getElementById('categoryTree');

    if (categoryTree) {
        new Sortable(categoryTree.querySelector('.category-list'), {
            group: 'categories',
            animation: 150,
            handle: '.category-drag-handle',
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                updateCategoryOrder();
            }
        });
    }
});

function updateCategoryOrder() {
    const items = document.querySelectorAll('.category-item');
    const orderData = [];

    items.forEach((item, index) => {
        orderData.push({
            id: item.dataset.id,
            order: index + 1,
            parent: null // Simplified for now
        });
    });

    fetch('categories.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=reorder&order_data=' + encodeURIComponent(JSON.stringify(orderData)) + '&csrf_token=<?php echo generateCSRFToken(); ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Category order updated successfully', 'success');
        } else {
            showNotification('Failed to update category order', 'error');
        }
    });
}

function editCategory(categoryId) {
    // Find category data
    const categories = <?php echo json_encode($categories); ?>;
    const category = categories.find(c => c.id == categoryId);

    if (category) {
        document.getElementById('modalTitle').textContent = 'Edit Category';
        document.getElementById('formAction').value = 'edit';
        document.getElementById('categoryId').value = category.id;
        document.getElementById('categoryName').value = category.name || '';
        document.getElementById('categoryDescription').value = category.description || '';
        document.getElementById('categoryImage').value = category.image || '';
        document.getElementById('parentCategory').value = category.parent_id || '';
        document.getElementById('sortOrder').value = category.sort_order || 1;
        document.getElementById('seoTitle').value = category.seo_title || '';
        document.getElementById('seoDescription').value = category.seo_description || '';
        document.getElementById('categoryActive').checked = category.status === 'active';

        new bootstrap.Modal(document.getElementById('categoryModal')).show();
    }
}

function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="category_id" value="${categoryId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset form when modal is hidden
document.getElementById('categoryModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('categoryForm').reset();
    document.getElementById('modalTitle').textContent = 'Add Category';
    document.getElementById('formAction').value = 'add';
    document.getElementById('categoryId').value = '';
});

// Bulk operations functions
function selectAllCategories() {
    const checkboxes = document.querySelectorAll('.category-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.category-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.category-select:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

function confirmBulkAction() {
    const selectedCheckboxes = document.querySelectorAll('.category-select:checked');
    const action = document.querySelector('select[name="bulk_action"]').value;

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one category.');
        return false;
    }

    if (!action) {
        alert('Please select an action.');
        return false;
    }

    const actionText = action === 'delete' ? 'delete' : action;
    const confirmMessage = `Are you sure you want to ${actionText} ${selectedCheckboxes.length} selected categories?`;

    if (action === 'delete') {
        return confirm(confirmMessage + ' This action cannot be undone.');
    }

    return confirm(confirmMessage);
}

// Add selected categories to bulk form before submission
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    const selectedCheckboxes = document.querySelectorAll('.category-select:checked');

    // Remove existing hidden inputs
    const existingInputs = this.querySelectorAll('input[name="selected_categories[]"]');
    existingInputs.forEach(input => input.remove());

    // Add selected categories as hidden inputs
    selectedCheckboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'selected_categories[]';
        hiddenInput.value = checkbox.value;
        this.appendChild(hiddenInput);
    });
});
</script>


