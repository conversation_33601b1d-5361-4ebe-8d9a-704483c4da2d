<?php
/**
 * CYPTSHOP Admin Theme Color Management
 * Phase 2: Dynamic Theme System
 */

$pageTitle = 'Theme Colors';
$breadcrumbs = [
    ['title' => 'Settings', 'url' => 'settings.php'],
    ['title' => 'Theme Colors']
];

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Theme management functions
function getCurrentThemeColors() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_group = 'theme_colors'");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        return [
            'primary_color' => $settings['primary_color'] ?? '#00FFFF',
            'secondary_color' => $settings['secondary_color'] ?? '#FF00FF',
            'accent_color' => $settings['accent_color'] ?? '#FFFF00',
            'background_color' => $settings['background_color'] ?? '#000000',
            'text_color' => $settings['text_color'] ?? '#FFFFFF'
        ];
    } catch (Exception $e) {
        error_log("Theme colors fetch error: " . $e->getMessage());
        return [
            'primary_color' => '#00FFFF',
            'secondary_color' => '#FF00FF',
            'accent_color' => '#FFFF00',
            'background_color' => '#000000',
            'text_color' => '#FFFFFF'
        ];
    }
}

function saveThemeColors($colors) {
    global $pdo;
    try {
        $pdo->beginTransaction();

        foreach ($colors as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_group, setting_type)
                VALUES (?, ?, 'theme_colors', 'color')
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ");
            $stmt->execute([$key, $value]);
        }

        $pdo->commit();

        // Generate CSS file
        generateThemeCSS($colors);

        return true;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Theme save error: " . $e->getMessage());
        return false;
    }
}

function generateThemeCSS($colors) {
    $cssContent = ":root {\n";
    $cssContent .= "    --cyan: {$colors['primary_color']};\n";
    $cssContent .= "    --magenta: {$colors['secondary_color']};\n";
    $cssContent .= "    --yellow: {$colors['accent_color']};\n";
    $cssContent .= "    --black: {$colors['background_color']};\n";
    $cssContent .= "    --white: {$colors['text_color']};\n";
    $cssContent .= "    --off-white: " . adjustBrightness($colors['text_color'], -20) . ";\n";
    $cssContent .= "}\n";

    // Write to theme file
    $themeFile = BASE_PATH . 'assets/css/theme-custom.css';
    file_put_contents($themeFile, $cssContent);
}

function adjustBrightness($hex, $percent) {
    $hex = str_replace('#', '', $hex);
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));

    return sprintf("#%02x%02x%02x", $r, $g, $b);
}

function getColorPresets() {
    return [
        [
            'id' => 1,
            'name' => 'CYPTSHOP Default',
            'primary' => '#00FFFF',
            'secondary' => '#FF00FF',
            'accent' => '#FFFF00',
            'background' => '#000000'
        ],
        [
            'id' => 2,
            'name' => 'Ocean Blue',
            'primary' => '#0066CC',
            'secondary' => '#0099FF',
            'accent' => '#00CCFF',
            'background' => '#001122'
        ],
        [
            'id' => 3,
            'name' => 'Forest Green',
            'primary' => '#00AA44',
            'secondary' => '#00CC55',
            'accent' => '#00FF66',
            'background' => '#001100'
        ]
    ];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errorMessage = 'Invalid security token.';
    } else {
        if ($_POST['action'] === 'save_theme') {
            // Validate and save theme colors
            $themeColors = [
                'primary_color' => $_POST['primary_color'] ?? '#00FFFF',
                'secondary_color' => $_POST['secondary_color'] ?? '#FF00FF',
                'accent_color' => $_POST['accent_color'] ?? '#FFFF00',
                'background_color' => $_POST['background_color'] ?? '#000000',
                'text_color' => $_POST['text_color'] ?? '#FFFFFF'
            ];

            // Validate hex colors
            $validColors = true;
            foreach ($themeColors as $key => $color) {
                if (!preg_match('/^#[a-fA-F0-9]{6}$/', $color)) {
                    $validColors = false;
                    break;
                }
            }

            if (!$validColors) {
                $errorMessage = 'Invalid color format. Please use valid hex colors.';
            } else {
                if (saveThemeColors($themeColors)) {
                    $successMessage = 'Theme colors saved successfully! Changes will be visible on the next page load.';
                } else {
                    $errorMessage = 'Failed to save theme colors. Please try again.';
                }
            }
        } elseif ($_POST['action'] === 'upload_theme') {
            // Handle theme file upload
            if (isset($_FILES['theme_file']) && $_FILES['theme_file']['error'] === UPLOAD_ERR_OK) {
                $uploadedFile = $_FILES['theme_file'];
                $fileContent = file_get_contents($uploadedFile['tmp_name']);

                try {
                    $themeData = json_decode($fileContent, true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($themeData['colors'])) {
                        if (saveThemeColors($themeData['colors'])) {
                            $successMessage = 'Theme uploaded and applied successfully!';
                        } else {
                            $errorMessage = 'Failed to apply uploaded theme.';
                        }
                    } else {
                        $errorMessage = 'Invalid theme file format.';
                    }
                } catch (Exception $e) {
                    $errorMessage = 'Error processing theme file: ' . $e->getMessage();
                }
            } else {
                $errorMessage = 'No theme file uploaded or upload error.';
            }
        }
    }
}

// Get current theme settings
$currentTheme = getCurrentThemeColors();

// Get available presets
$colorPresets = getColorPresets();

$pageTitle = 'Theme Management - Admin';
$bodyClass = 'admin-themes';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
            <h1 class="h2 text-white">
                <i class="fas fa-palette me-2"></i>Theme Color Management
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <button class="btn btn-outline-secondary me-2" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-1"></i>Reset to Defaults
                </button>
                <button class="btn btn-cyan" onclick="previewTheme()">
                    <i class="fas fa-eye me-1"></i>Preview Changes
                </button>
            </div>
        </div>

        <?php if (isset($successMessage)): ?>
        <div class="alert alert-success bg-dark-grey-2 border-success text-success">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($successMessage); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
        <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($errorMessage); ?>
        </div>
        <?php endif; ?>



<!-- Theme Preset Gallery - Task *******.2.1 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-dark-grey-1 border-cyan">
            <div class="card-header bg-dark-grey-2 border-cyan">
                <h5 class="mb-0 text-cyan">
                    <i class="fas fa-swatchbook me-2"></i>Theme Preset Gallery
                </h5>
                <small class="text-muted">Choose from professionally designed theme presets</small>
            </div>
            <div class="card-body">
                <div id="presetGallery" class="preset-gallery">
                    <div class="row g-3">
                        <?php foreach ($colorPresets as $preset): ?>
                        <div class="col-md-4">
                            <div class="preset-card bg-dark-grey-2 border-dark-grey-3 rounded p-3"
                                 onclick="applyPreset(<?php echo htmlspecialchars(json_encode($preset)); ?>)">
                                <div class="preset-header d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-white mb-0"><?php echo htmlspecialchars($preset['name']); ?></h6>
                                    <button class="btn btn-sm btn-outline-cyan" type="button">Apply</button>
                                </div>
                                <div class="preset-colors d-flex gap-2 mb-2">
                                    <div class="color-dot" style="background: <?php echo $preset['primary']; ?>;" title="Primary"></div>
                                    <div class="color-dot" style="background: <?php echo $preset['secondary']; ?>;" title="Secondary"></div>
                                    <div class="color-dot" style="background: <?php echo $preset['accent']; ?>;" title="Accent"></div>
                                    <div class="color-dot" style="background: <?php echo $preset['background']; ?>; border: 1px solid #333;" title="Background"></div>
                                </div>
                                <small class="text-muted">Click to apply this color scheme</small>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <!-- Upload Custom Theme Card -->
                        <div class="col-md-4">
                            <div class="preset-card bg-dark-grey-2 border-cyan border-dashed rounded p-3 text-center"
                                 onclick="document.getElementById('customThemeUpload').click()">
                                <div class="upload-icon mb-2">
                                    <i class="fas fa-upload fa-2x text-cyan"></i>
                                </div>
                                <h6 class="text-white mb-1">Upload Custom Theme</h6>
                                <small class="text-muted">Upload a .json theme file</small>
                                <form method="POST" enctype="multipart/form-data" style="display: none;" id="uploadForm">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="upload_theme">
                                    <input type="file" id="customThemeUpload" name="theme_file" accept=".json,.cypttheme" onchange="this.form.submit()">
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Color Management Panel -->
    <div class="col-lg-8">
        <div class="card bg-dark-grey-1 border-cyan">
            <div class="card-header bg-dark-grey-2 border-cyan">
                <h5 class="mb-0 text-cyan">
                    <i class="fas fa-paint-brush me-2"></i>CYPTSHOP Color Scheme
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="themeForm" data-ajax="true">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="save_theme">
                    
                    <div class="row">
                        <!-- Primary Color -->
                        <div class="col-md-6 mb-4">
                            <label for="primary_color" class="form-label text-white">
                                <i class="fas fa-circle text-cyan me-1"></i>Primary Color (Cyan)
                            </label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color bg-dark-grey-2 border-dark-grey-3"
                                       id="primary_color" name="primary_color"
                                       value="<?php echo htmlspecialchars($currentTheme['primary_color']); ?>"
                                       onchange="updatePreview()">
                                <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                       value="<?php echo htmlspecialchars($currentTheme['primary_color']); ?>"
                                       onchange="updateColorPicker('primary_color', this.value)">
                            </div>
                            <small class="text-muted">Main brand color used for buttons, links, and accents</small>
                        </div>
                        
                        <!-- Secondary Color -->
                        <div class="col-md-6 mb-4">
                            <label for="secondary_color" class="form-label text-white">
                                <i class="fas fa-circle text-magenta me-1"></i>Secondary Color (Magenta)
                            </label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color bg-dark-grey-2 border-dark-grey-3"
                                       id="secondary_color" name="secondary_color"
                                       value="<?php echo htmlspecialchars($currentTheme['secondary_color']); ?>"
                                       onchange="updatePreview()">
                                <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                       value="<?php echo htmlspecialchars($currentTheme['secondary_color']); ?>"
                                       onchange="updateColorPicker('secondary_color', this.value)">
                            </div>
                            <small class="text-muted">Secondary brand color for highlights and badges</small>
                        </div>

                        <!-- Accent Color -->
                        <div class="col-md-6 mb-4">
                            <label for="accent_color" class="form-label text-white">
                                <i class="fas fa-circle text-yellow me-1"></i>Accent Color (Yellow)
                            </label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color bg-dark-grey-2 border-dark-grey-3"
                                       id="accent_color" name="accent_color"
                                       value="<?php echo htmlspecialchars($currentTheme['accent_color']); ?>"
                                       onchange="updatePreview()">
                                <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                       value="<?php echo htmlspecialchars($currentTheme['accent_color']); ?>"
                                       onchange="updateColorPicker('accent_color', this.value)">
                            </div>
                            <small class="text-muted">Accent color for warnings and special elements</small>
                        </div>

                        <!-- Background Color -->
                        <div class="col-md-6 mb-4">
                            <label for="background_color" class="form-label text-white">
                                <i class="fas fa-circle text-dark me-1"></i>Background Color (Black)
                            </label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color bg-dark-grey-2 border-dark-grey-3"
                                       id="background_color" name="background_color"
                                       value="<?php echo htmlspecialchars($currentTheme['background_color']); ?>"
                                       onchange="updatePreview()">
                                <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                       value="<?php echo htmlspecialchars($currentTheme['background_color']); ?>"
                                       onchange="updateColorPicker('background_color', this.value)">
                            </div>
                            <small class="text-muted">Main background color for the site</small>
                        </div>

                        <!-- Text Color -->
                        <div class="col-md-6 mb-4">
                            <label for="text_color" class="form-label text-white">
                                <i class="fas fa-circle text-white me-1"></i>Text Color (White)
                            </label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color bg-dark-grey-2 border-dark-grey-3"
                                       id="text_color" name="text_color"
                                       value="<?php echo htmlspecialchars($currentTheme['text_color']); ?>"
                                       onchange="updatePreview()">
                                <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                       value="<?php echo htmlspecialchars($currentTheme['text_color']); ?>"
                                       onchange="updateColorPicker('text_color', this.value)">
                            </div>
                            <small class="text-muted">Primary text color</small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div class="theme-sharing-actions">
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="exportCurrentTheme()">
                                <i class="fas fa-download me-1"></i>Export Theme
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="showImportDialog()">
                                <i class="fas fa-upload me-1"></i>Import Theme
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="generateShareLink()">
                                <i class="fas fa-share-alt me-1"></i>Share Theme
                            </button>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>Save Theme Colors
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Live Preview Panel -->
    <div class="col-lg-4">
        <div class="card bg-dark-grey-1 border-magenta">
            <div class="card-header bg-dark-grey-2 border-magenta">
                <h5 class="mb-0 text-magenta">
                    <i class="fas fa-eye me-2"></i>Live Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="themePreview" class="theme-preview">
                    <div class="preview-header">
                        <h6>CYPTSHOP</h6>
                        <div class="preview-nav">
                            <span class="preview-link">Home</span>
                            <span class="preview-link active">Products</span>
                            <span class="preview-link">Contact</span>
                        </div>
                    </div>
                    <div class="preview-content">
                        <div class="preview-card">
                            <h6>Sample Product</h6>
                            <p>This is how your content will look with the selected colors.</p>
                            <button class="preview-btn">Add to Cart</button>
                        </div>
                        <div class="preview-sidebar">
                            <div class="preview-widget">
                                <h6>Categories</h6>
                                <ul>
                                    <li>T-Shirts</li>
                                    <li>Hoodies</li>
                                    <li>Accessories</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Changes are applied in real-time. Save to make them permanent.
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Color Palette Info -->
        <div class="card bg-dark-grey-1 border-yellow mt-4">
            <div class="card-header bg-dark-grey-2 border-yellow">
                <h5 class="mb-0 text-yellow">
                    <i class="fas fa-info-circle me-2"></i>CMYK Color System
                </h5>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-3">
                    CYPTSHOP uses a CMYK-inspired color scheme representing the printing industry:
                </p>
                <div class="color-info">
                    <div class="d-flex align-items-center mb-2">
                        <div class="color-swatch" style="background: #00FFFF;"></div>
                        <span class="small"><strong>Cyan:</strong> Primary brand color</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="color-swatch" style="background: #FF00FF;"></div>
                        <span class="small"><strong>Magenta:</strong> Secondary highlights</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="color-swatch" style="background: #FFFF00;"></div>
                        <span class="small"><strong>Yellow:</strong> Accent and warnings</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="color-swatch" style="background: #000000; border: 1px solid #333;"></div>
                        <span class="small"><strong>Black:</strong> Background and text</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Theme Sharing Modals - Task *******.2.4 -->
<!-- Export Theme Modal -->
<div class="modal fade" id="exportThemeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content admin-modal">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download me-2"></i>Export Theme
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportThemeForm">
                    <div class="mb-3">
                        <label for="exportThemeName" class="form-label">Theme Name</label>
                        <input type="text" class="form-control" id="exportThemeName"
                               placeholder="My Custom Theme" required>
                    </div>
                    <div class="mb-3">
                        <label for="exportThemeDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="exportThemeDescription" rows="3"
                                  placeholder="Describe your theme..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="exportThemeTags" class="form-label">Tags</label>
                        <input type="text" class="form-control" id="exportThemeTags"
                               placeholder="dark, professional, blue (comma separated)">
                        <small class="text-muted">Add tags to help others find your theme</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-admin-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-admin-primary" onclick="downloadThemeFile()">
                    <i class="fas fa-download me-1"></i>Download Theme File
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Theme Modal -->
<div class="modal fade" id="importThemeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content admin-modal">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Import Theme
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="import-methods">
                    <div class="nav nav-tabs mb-3" role="tablist">
                        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#importFile">
                            <i class="fas fa-file me-1"></i>Upload File
                        </button>
                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#importText">
                            <i class="fas fa-code me-1"></i>Paste Code
                        </button>
                    </div>

                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="importFile">
                            <div class="mb-3">
                                <label for="themeFileInput" class="form-label">Select Theme File</label>
                                <input type="file" class="form-control" id="themeFileInput"
                                       accept=".cypttheme,.json" onchange="handleFileSelect(event)">
                                <small class="text-muted">Upload a .cypttheme or .json file</small>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="importText">
                            <div class="mb-3">
                                <label for="themeDataInput" class="form-label">Theme Data</label>
                                <textarea class="form-control" id="themeDataInput" rows="8"
                                          placeholder="Paste theme JSON data here..."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="import-options">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="applyImmediately" checked>
                            <label class="form-check-label" for="applyImmediately">
                                Apply theme immediately after import
                            </label>
                        </div>
                    </div>

                    <div id="importPreview" class="import-preview mt-3" style="display: none;">
                        <h6>Theme Preview:</h6>
                        <div class="theme-preview-card">
                            <div class="preview-info">
                                <strong id="previewName"></strong>
                                <p id="previewDescription" class="text-muted"></p>
                                <div id="previewTags" class="preview-tags"></div>
                            </div>
                            <div class="preview-colors">
                                <div class="color-swatch" id="previewPrimary"></div>
                                <div class="color-swatch" id="previewSecondary"></div>
                                <div class="color-swatch" id="previewAccent"></div>
                                <div class="color-swatch" id="previewBackground"></div>
                                <div class="color-swatch" id="previewText"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-admin-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-admin-primary" id="importThemeBtn"
                        onclick="importThemeData()" disabled>
                    <i class="fas fa-upload me-1"></i>Import Theme
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Share Theme Modal -->
<div class="modal fade" id="shareThemeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content admin-modal">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share-alt me-2"></i>Share Theme
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="share-options">
                    <div class="mb-3">
                        <label class="form-label">Share Link</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shareThemeUrl" readonly>
                            <button class="btn btn-admin-secondary" onclick="copyShareLink()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <small class="text-muted">This link will expire in 30 days</small>
                    </div>

                    <div class="share-actions">
                        <button class="btn btn-admin-secondary me-2" onclick="shareViaEmail()">
                            <i class="fas fa-envelope me-1"></i>Email
                        </button>
                        <button class="btn btn-admin-secondary me-2" onclick="shareViaTwitter()">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </button>
                        <button class="btn btn-admin-secondary" onclick="shareViaFacebook()">
                            <i class="fab fa-facebook me-1"></i>Facebook
                        </button>
                    </div>
                </div>

                <div id="shareThemeLoading" class="text-center" style="display: none;">
                    <div class="spinner-border text-admin-primary" role="status">
                        <span class="visually-hidden">Generating share link...</span>
                    </div>
                    <p class="mt-2">Generating share link...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-admin-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.form-control-color {
    width: 60px;
    height: 38px;
    border-radius: 6px 0 0 6px;
}

/* Preset Gallery Styles */
.preset-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.preset-card:hover {
    border-color: var(--cyan);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
}

.color-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.border-dashed {
    border-style: dashed !important;
}

.theme-preview {
    background: var(--preview-background, #000000);
    border-radius: 8px;
    color: var(--preview-text, #FFFFFF);
    padding: 15px;
    min-height: 200px;
}

.theme-preview .preview-header {
    border-bottom: 1px solid var(--preview-primary, #00FFFF);
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.theme-preview .preview-header h6 {
    color: var(--preview-primary, #00FFFF);
    margin: 0;
}

.theme-preview .preview-nav {
    margin-top: 8px;
}

.theme-preview .preview-link {
    color: var(--preview-text, #FFFFFF);
    margin-right: 15px;
    padding: 5px 0;
    border-bottom: 2px solid transparent;
}

.theme-preview .preview-link.active {
    color: var(--preview-primary, #00FFFF);
    border-bottom-color: var(--preview-primary, #00FFFF);
}

.theme-preview .preview-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--preview-secondary, #FF00FF);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.theme-preview .preview-card h6 {
    color: var(--preview-secondary, #FF00FF);
    margin-bottom: 8px;
}

.theme-preview .preview-btn {
    background: var(--preview-primary, #00FFFF);
    color: var(--preview-background, #000000);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
}

.theme-preview .preview-sidebar {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 6px;
    padding: 12px;
}

.theme-preview .preview-widget h6 {
    color: var(--preview-accent, #FFFF00);
    margin-bottom: 8px;
}

.theme-preview .preview-widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.theme-preview .preview-widget li {
    color: var(--preview-text, #FFFFFF);
    padding: 4px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
    overflow: hidden;
    border: 1px solid var(--admin-border);
}

.preview-header {
    background: var(--admin-primary);
    color: var(--admin-darkest);
    padding: 10px 15px;
    font-weight: bold;
}

.preview-nav {
    margin-top: 5px;
}

.preview-link {
    margin-right: 15px;
    opacity: 0.7;
    font-size: 0.9rem;
}

.preview-link.active {
    opacity: 1;
    text-decoration: underline;
}

.preview-content {
    padding: 15px;
}

.preview-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 3px solid var(--admin-secondary);
}

.preview-card h6 {
    color: var(--admin-primary);
    margin-bottom: 8px;
}

.preview-btn {
    background: var(--admin-secondary);
    color: var(--admin-darkest);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.preview-sidebar {
    background: rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 6px;
}

.preview-widget h6 {
    color: var(--admin-accent);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.preview-widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.preview-widget li {
    padding: 3px 0;
    font-size: 0.8rem;
    color: var(--admin-text-secondary);
}

.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    margin-right: 10px;
    flex-shrink: 0;
}

.color-info .small {
    color: var(--admin-text-secondary);
}

/* Theme Preset Gallery Styles - Task *******.2.1 */
.preset-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.preset-card {
    background: var(--admin-darker);
    border: 2px solid var(--admin-border);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.preset-card:hover {
    border-color: var(--admin-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.15);
}

.preset-card.active {
    border-color: var(--admin-primary);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.preset-header {
    padding: 15px;
    border-bottom: 1px solid var(--admin-border);
}

.preset-title {
    font-weight: 600;
    color: var(--admin-text);
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.preset-description {
    color: var(--admin-text-secondary);
    font-size: 0.85rem;
    margin: 0;
    line-height: 1.4;
}

.preset-colors {
    display: flex;
    height: 60px;
}

.preset-color {
    flex: 1;
    position: relative;
}

.preset-color:first-child {
    border-radius: 0 0 0 0;
}

.preset-color:last-child {
    border-radius: 0 0 0 0;
}

.preset-actions {
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preset-category {
    background: var(--admin-primary);
    color: var(--admin-darkest);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.preset-apply-btn {
    background: var(--admin-secondary);
    color: var(--admin-darkest);
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.preset-apply-btn:hover {
    background: var(--admin-accent);
    transform: scale(1.05);
}

.preset-tags {
    margin-top: 8px;
}

.preset-tag {
    background: rgba(255, 255, 255, 0.1);
    color: var(--admin-text-secondary);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7rem;
    margin-right: 4px;
    display: inline-block;
}

.category-filter {
    margin-bottom: 20px;
}

.category-btn {
    background: transparent;
    border: 1px solid var(--admin-border);
    color: var(--admin-text-secondary);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-right: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.category-btn:hover,
.category-btn.active {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: var(--admin-darkest);
}

/* Theme Sharing Styles - Task *******.2.4 */
.theme-sharing-actions {
    display: flex;
    gap: 8px;
}

.admin-modal .modal-content {
    background: var(--admin-darker);
    border: 1px solid var(--admin-border);
    border-radius: 12px;
}

.admin-modal .modal-header {
    background: linear-gradient(135deg, var(--admin-darker) 0%, var(--admin-darkest) 100%);
    border-bottom: 1px solid var(--admin-border);
    color: var(--admin-text);
}

.admin-modal .modal-title {
    color: var(--admin-primary);
    font-weight: 600;
}

.admin-modal .modal-body {
    background: var(--admin-darker);
    color: var(--admin-text);
}

.admin-modal .form-label {
    color: var(--admin-text);
    font-weight: 600;
}

.admin-modal .form-control {
    background: var(--admin-darkest);
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
}

.admin-modal .form-control:focus {
    background: var(--admin-darkest);
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
    color: var(--admin-text);
}

.admin-modal .nav-tabs {
    border-bottom: 1px solid var(--admin-border);
}

.admin-modal .nav-link {
    background: transparent;
    border: 1px solid transparent;
    color: var(--admin-text-secondary);
}

.admin-modal .nav-link.active {
    background: var(--admin-darkest);
    border-color: var(--admin-border) var(--admin-border) transparent;
    color: var(--admin-primary);
}

.import-preview {
    background: var(--admin-darkest);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 15px;
}

.theme-preview-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-info {
    flex: 1;
}

.preview-colors {
    display: flex;
    gap: 4px;
}

.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid var(--admin-border);
}

.preview-tags {
    margin-top: 8px;
}

.preview-tag {
    background: var(--admin-primary);
    color: var(--admin-darkest);
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: 4px;
    display: inline-block;
}

.share-options {
    text-align: center;
}

.share-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.input-group .btn {
    border-color: var(--admin-border);
}

.form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}
</style>

<script>
// Theme presets
const themePresets = {
    detroit: {
        primary_color: '#00FFFF',
        secondary_color: '#FF00FF', 
        accent_color: '#FFFF00',
        background_color: '#000000',
        text_color: '#FFFFFF'
    },
    cmyk: {
        primary_color: '#00CCCC',
        secondary_color: '#CC00CC',
        accent_color: '#CCCC00', 
        background_color: '#1a1a1a',
        text_color: '#F0F0F0'
    },
    neon: {
        primary_color: '#00FF88',
        secondary_color: '#FF0088',
        accent_color: '#FFAA00',
        background_color: '#0a0a0a', 
        text_color: '#FFFFFF'
    }
};

function loadPreset(presetName) {
    const preset = themePresets[presetName];
    if (!preset) return;
    
    Object.keys(preset).forEach(key => {
        const colorInput = document.getElementById(key);
        const textInput = colorInput.nextElementSibling;
        
        colorInput.value = preset[key];
        textInput.value = preset[key];
    });
    
    updatePreview();
    NotificationManager.show(`Loaded ${presetName} theme preset`, 'info');
}

function updateColorPicker(colorId, value) {
    const colorInput = document.getElementById(colorId);
    colorInput.value = value;
    updatePreview();
}

function updatePreview() {
    const colors = {
        primary: document.getElementById('primary_color').value,
        secondary: document.getElementById('secondary_color').value,
        accent: document.getElementById('accent_color').value,
        background: document.getElementById('background_color').value,
        text: document.getElementById('text_color').value
    };
    
    // Update CSS custom properties for preview
    const root = document.documentElement;
    root.style.setProperty('--admin-primary', colors.primary);
    root.style.setProperty('--admin-secondary', colors.secondary);
    root.style.setProperty('--admin-accent', colors.accent);
    
    // Update text inputs
    Object.keys(colors).forEach(key => {
        const colorId = key + '_color';
        const textInput = document.getElementById(colorId).nextElementSibling;
        textInput.value = colors[key.replace('_', '')];
    });
}

function resetToDefaults() {
    if (confirm('Reset all colors to default CMYK theme?')) {
        loadPreset('detroit');
    }
}

function previewTheme() {
    // Open main site in new tab with current theme
    const colors = {
        primary_color: document.getElementById('primary_color').value,
        secondary_color: document.getElementById('secondary_color').value,
        accent_color: document.getElementById('accent_color').value,
        background_color: document.getElementById('background_color').value,
        text_color: document.getElementById('text_color').value
    };
    
    // Store in session storage for preview
    sessionStorage.setItem('themePreview', JSON.stringify(colors));
    window.open('<?php echo SITE_URL; ?>/?preview=1', '_blank');
}

// Theme Preset Gallery Functions - Task *******.2.1 & *******.2.2
let allPresets = {};
let currentCategory = 'all';

async function loadThemePresets() {
    try {
        const response = await fetch('ajax/themes.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'get_all_presets' })
        });

        const data = await response.json();
        if (data.success) {
            allPresets = data.data.presets;
            renderPresetGallery(data.data);
        } else {
            throw new Error(data.message || 'Failed to load presets');
        }
    } catch (error) {
        console.error('Error loading presets:', error);
        document.getElementById('presetGallery').innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle mb-2"></i>
                <p>Failed to load theme presets</p>
                <button class="btn btn-admin-secondary btn-sm" onclick="loadThemePresets()">
                    <i class="fas fa-redo me-1"></i>Retry
                </button>
            </div>
        `;
    }
}

function renderPresetGallery(data) {
    const gallery = document.getElementById('presetGallery');

    // Create category filter
    let categoryFilter = '<div class="category-filter">';
    categoryFilter += '<button class="category-btn active" onclick="filterByCategory(\'all\')">All Themes</button>';

    Object.entries(data.categories).forEach(([key, label]) => {
        categoryFilter += `<button class="category-btn" onclick="filterByCategory('${key}')">${label}</button>`;
    });
    categoryFilter += '</div>';

    // Create preset cards
    let presetCards = '<div class="preset-cards">';
    Object.entries(data.presets).forEach(([presetKey, preset]) => {
        const colors = preset.colors;
        const metadata = preset.metadata;

        presetCards += `
            <div class="preset-card" data-category="${metadata.category}" data-preset="${presetKey}">
                <div class="preset-header">
                    <h6 class="preset-title">${metadata.name}</h6>
                    <p class="preset-description">${metadata.description}</p>
                    <div class="preset-tags">
                        ${metadata.tags.map(tag => `<span class="preset-tag">${tag}</span>`).join('')}
                    </div>
                </div>
                <div class="preset-colors">
                    <div class="preset-color" style="background: ${colors.primary_color}" title="Primary: ${colors.primary_color}"></div>
                    <div class="preset-color" style="background: ${colors.secondary_color}" title="Secondary: ${colors.secondary_color}"></div>
                    <div class="preset-color" style="background: ${colors.accent_color}" title="Accent: ${colors.accent_color}"></div>
                    <div class="preset-color" style="background: ${colors.background_color}" title="Background: ${colors.background_color}"></div>
                    <div class="preset-color" style="background: ${colors.text_color}" title="Text: ${colors.text_color}"></div>
                </div>
                <div class="preset-actions">
                    <span class="preset-category">${metadata.category}</span>
                    <button class="preset-apply-btn" onclick="applyPresetTheme('${presetKey}')">
                        <i class="fas fa-check me-1"></i>Apply
                    </button>
                </div>
            </div>
        `;
    });
    presetCards += '</div>';

    gallery.innerHTML = categoryFilter + presetCards;

    // Add click handlers for preview
    document.querySelectorAll('.preset-card').forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.classList.contains('preset-apply-btn')) {
                const presetKey = this.dataset.preset;
                previewPreset(presetKey);
            }
        });
    });
}

function filterByCategory(category) {
    currentCategory = category;

    // Update active button
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Filter cards
    document.querySelectorAll('.preset-card').forEach(card => {
        if (category === 'all' || card.dataset.category === category) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function previewPreset(presetKey) {
    const preset = allPresets[presetKey];
    if (!preset) return;

    const colors = preset.colors;

    // Update form inputs
    Object.entries(colors).forEach(([key, value]) => {
        const colorInput = document.getElementById(key);
        const textInput = colorInput?.nextElementSibling;

        if (colorInput) {
            colorInput.value = value;
        }
        if (textInput) {
            textInput.value = value;
        }
    });

    // Update preview
    updatePreview();

    // Highlight selected card
    document.querySelectorAll('.preset-card').forEach(card => {
        card.classList.remove('active');
    });
    document.querySelector(`[data-preset="${presetKey}"]`)?.classList.add('active');

    NotificationManager.show(`Previewing ${preset.metadata.name} theme`, 'info');
}

async function applyPresetTheme(presetKey) {
    const preset = allPresets[presetKey];
    if (!preset) return;

    if (!confirm(`Apply "${preset.metadata.name}" theme? This will save the theme colors.`)) {
        return;
    }

    try {
        const response = await fetch('ajax/themes.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'apply_preset',
                preset: presetKey,
                save_as_active: true
            })
        });

        const data = await response.json();
        if (data.success) {
            NotificationManager.show(data.data.message, 'success');
            previewPreset(presetKey);
        } else {
            throw new Error(data.message || 'Failed to apply preset');
        }
    } catch (error) {
        console.error('Error applying preset:', error);
        NotificationManager.show('Failed to apply theme preset', 'error');
    }
}

// Theme Sharing Functions - Task *******.2.4
let currentThemeData = null;

// Export current theme
function exportCurrentTheme() {
    const modal = new bootstrap.Modal(document.getElementById('exportThemeModal'));
    modal.show();
}

async function downloadThemeFile() {
    const themeName = document.getElementById('exportThemeName').value.trim();
    const description = document.getElementById('exportThemeDescription').value.trim();
    const tags = document.getElementById('exportThemeTags').value.split(',').map(tag => tag.trim()).filter(tag => tag);

    if (!themeName) {
        NotificationManager.show('Please enter a theme name', 'error');
        return;
    }

    try {
        const response = await fetch('/admin/ajax/theme-sharing.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'export_theme',
                theme_name: themeName,
                description: description,
                tags: tags
            })
        });

        const data = await response.json();

        if (data.success) {
            // Create download link
            const blob = new Blob([data.data.json], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.data.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            NotificationManager.show('Theme exported successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('exportThemeModal')).hide();
        } else {
            NotificationManager.show(data.message, 'error');
        }
    } catch (error) {
        NotificationManager.show('Failed to export theme', 'error');
    }
}

// Import theme
function showImportDialog() {
    const modal = new bootstrap.Modal(document.getElementById('importThemeModal'));
    modal.show();
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const themeData = JSON.parse(e.target.result);
            currentThemeData = themeData;
            showThemePreview(themeData);
            document.getElementById('importThemeBtn').disabled = false;
        } catch (error) {
            NotificationManager.show('Invalid theme file format', 'error');
            document.getElementById('importThemeBtn').disabled = true;
        }
    };
    reader.readAsText(file);
}

// Handle paste input
document.getElementById('themeDataInput').addEventListener('input', function() {
    try {
        const themeData = JSON.parse(this.value);
        currentThemeData = themeData;
        showThemePreview(themeData);
        document.getElementById('importThemeBtn').disabled = false;
    } catch (error) {
        document.getElementById('importPreview').style.display = 'none';
        document.getElementById('importThemeBtn').disabled = true;
    }
});

function showThemePreview(themeData) {
    document.getElementById('previewName').textContent = themeData.name || 'Unnamed Theme';
    document.getElementById('previewDescription').textContent = themeData.description || 'No description';

    // Show tags
    const tagsContainer = document.getElementById('previewTags');
    tagsContainer.innerHTML = '';
    if (themeData.tags && themeData.tags.length > 0) {
        themeData.tags.forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = 'preview-tag';
            tagSpan.textContent = tag;
            tagsContainer.appendChild(tagSpan);
        });
    }

    // Show color swatches
    const colors = themeData.theme_data || {};
    document.getElementById('previewPrimary').style.backgroundColor = colors.primary_color || '#00FFFF';
    document.getElementById('previewSecondary').style.backgroundColor = colors.secondary_color || '#FF00FF';
    document.getElementById('previewAccent').style.backgroundColor = colors.accent_color || '#FFFF00';
    document.getElementById('previewBackground').style.backgroundColor = colors.background_color || '#000000';
    document.getElementById('previewText').style.backgroundColor = colors.text_color || '#FFFFFF';

    document.getElementById('importPreview').style.display = 'block';
}

async function importThemeData() {
    if (!currentThemeData) {
        NotificationManager.show('No theme data to import', 'error');
        return;
    }

    const applyImmediately = document.getElementById('applyImmediately').checked;

    try {
        const response = await fetch('/admin/ajax/theme-sharing.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'import_theme',
                theme_data: currentThemeData,
                apply_immediately: applyImmediately
            })
        });

        const data = await response.json();

        if (data.success) {
            NotificationManager.show(data.data.message, 'success');

            if (applyImmediately) {
                // Refresh page to show new theme
                setTimeout(() => location.reload(), 1000);
            }

            bootstrap.Modal.getInstance(document.getElementById('importThemeModal')).hide();
        } else {
            NotificationManager.show(data.message, 'error');
        }
    } catch (error) {
        NotificationManager.show('Failed to import theme', 'error');
    }
}

// Generate share link
async function generateShareLink() {
    const modal = new bootstrap.Modal(document.getElementById('shareThemeModal'));
    modal.show();

    // Show loading
    document.querySelector('.share-options').style.display = 'none';
    document.getElementById('shareThemeLoading').style.display = 'block';

    try {
        const response = await fetch('/admin/ajax/theme-sharing.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'generate_share_link'
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('shareThemeUrl').value = data.data.share_url;
            document.querySelector('.share-options').style.display = 'block';
            document.getElementById('shareThemeLoading').style.display = 'none';
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        NotificationManager.show('Failed to generate share link', 'error');
        modal.hide();
    }
}

function copyShareLink() {
    const shareUrl = document.getElementById('shareThemeUrl');
    shareUrl.select();
    shareUrl.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        NotificationManager.show('Share link copied to clipboard!', 'success');
    } catch (error) {
        NotificationManager.show('Failed to copy link', 'error');
    }
}

function shareViaEmail() {
    const shareUrl = document.getElementById('shareThemeUrl').value;
    const subject = encodeURIComponent('Check out this custom theme!');
    const body = encodeURIComponent(`I created a custom theme and wanted to share it with you: ${shareUrl}`);
    window.open(`mailto:?subject=${subject}&body=${body}`);
}

function shareViaTwitter() {
    const shareUrl = document.getElementById('shareThemeUrl').value;
    const text = encodeURIComponent('Check out my custom theme! ');
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${encodeURIComponent(shareUrl)}`);
}

function shareViaFacebook() {
    const shareUrl = document.getElementById('shareThemeUrl').value;
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`);
}

// Apply preset colors
function applyPreset(preset) {
    if (confirm(`Apply the "${preset.name}" color scheme?`)) {
        document.getElementById('primary_color').value = preset.primary;
        document.getElementById('secondary_color').value = preset.secondary;
        document.getElementById('accent_color').value = preset.accent;
        document.getElementById('background_color').value = preset.background;
        document.getElementById('text_color').value = '#FFFFFF';

        // Update text inputs
        document.querySelector('input[onchange*="primary_color"]').value = preset.primary;
        document.querySelector('input[onchange*="secondary_color"]').value = preset.secondary;
        document.querySelector('input[onchange*="accent_color"]').value = preset.accent;
        document.querySelector('input[onchange*="background_color"]').value = preset.background;
        document.querySelector('input[onchange*="text_color"]').value = '#FFFFFF';

        updatePreview();
    }
}

// Update live preview
function updatePreview() {
    const primary = document.getElementById('primary_color').value;
    const secondary = document.getElementById('secondary_color').value;
    const accent = document.getElementById('accent_color').value;
    const background = document.getElementById('background_color').value;
    const text = document.getElementById('text_color').value;

    const preview = document.getElementById('themePreview');
    if (preview) {
        preview.style.setProperty('--preview-primary', primary);
        preview.style.setProperty('--preview-secondary', secondary);
        preview.style.setProperty('--preview-accent', accent);
        preview.style.setProperty('--preview-background', background);
        preview.style.setProperty('--preview-text', text);
    }
}

// Update color picker when text input changes
function updateColorPicker(colorId, value) {
    const colorInput = document.getElementById(colorId);
    if (colorInput && /^#[0-9A-F]{6}$/i.test(value)) {
        colorInput.value = value;
        updatePreview();
    }
}

// Reset to defaults
function resetToDefaults() {
    if (confirm('Reset all colors to CYPTSHOP defaults?')) {
        document.getElementById('primary_color').value = '#00FFFF';
        document.getElementById('secondary_color').value = '#FF00FF';
        document.getElementById('accent_color').value = '#FFFF00';
        document.getElementById('background_color').value = '#000000';
        document.getElementById('text_color').value = '#FFFFFF';

        // Update text inputs
        document.querySelector('input[onchange*="primary_color"]').value = '#00FFFF';
        document.querySelector('input[onchange*="secondary_color"]').value = '#FF00FF';
        document.querySelector('input[onchange*="accent_color"]').value = '#FFFF00';
        document.querySelector('input[onchange*="background_color"]').value = '#000000';
        document.querySelector('input[onchange*="text_color"]').value = '#FFFFFF';

        updatePreview();
    }
}

// Preview theme changes
function previewTheme() {
    updatePreview();
    alert('Preview updated! Save changes to apply them permanently.');
}

// Initialize preview and load presets
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();

    // Add event listeners to color inputs
    document.querySelectorAll('input[type="color"]').forEach(input => {
        input.addEventListener('change', updatePreview);
    });

    // Add event listeners to text inputs
    document.querySelectorAll('input[type="text"][onchange*="updateColorPicker"]').forEach(input => {
        input.addEventListener('input', function() {
            const colorId = this.getAttribute('onchange').match(/updateColorPicker\('([^']+)'/)[1];
            updateColorPicker(colorId, this.value);
        });
    });
});
</script>

<?php
/**
 * Helper functions for theme management
 */


?>

    </div>
</div>

</body>
</html>
