<?php
/**
 * CYPTSHOP Portfolio Management System
 * Complete portfolio gallery management with bulk uploads
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get database connection
$pdo = getDatabaseConnection();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'create_gallery':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $client_name = trim($_POST['client_name'] ?? '');
                $project_date = !empty($_POST['project_date']) ? $_POST['project_date'] : null;
                $project_url = trim($_POST['project_url'] ?? '');
                $tags = array_filter(array_map('trim', explode(',', $_POST['tags'] ?? '')));
                $status = $_POST['status'] ?? 'active';
                $featured = isset($_POST['featured']);

                if (empty($name)) {
                    $error = 'Gallery name is required.';
                } else {
                    $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s]/', '', $name));
                    $slug = preg_replace('/\s+/', '-', $slug);
                    
                    try {
                        $stmt = $pdo->prepare("
                            INSERT INTO portfolio_galleries (name, slug, description, category, client_name, project_date, project_url, tags, status, featured)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $slug, $description, $category, $client_name, $project_date, $project_url, json_encode($tags), $status, $featured])) {
                            $galleryId = $pdo->lastInsertId();
                            $success = 'Gallery created successfully! You can now add images to it.';
                            
                            // Redirect to gallery edit page
                            header("Location: /admin/portfolio.php?edit_gallery=$galleryId&success=" . urlencode($success));
                            exit;
                        } else {
                            $error = 'Failed to create gallery.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'edit_gallery':
                $galleryId = intval($_POST['gallery_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $client_name = trim($_POST['client_name'] ?? '');
                $project_date = !empty($_POST['project_date']) ? $_POST['project_date'] : null;
                $project_url = trim($_POST['project_url'] ?? '');
                $tags = array_filter(array_map('trim', explode(',', $_POST['tags'] ?? '')));
                $status = $_POST['status'] ?? 'active';
                $featured = isset($_POST['featured']);

                if (empty($name)) {
                    $error = 'Gallery name is required.';
                } else {
                    $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s]/', '', $name));
                    $slug = preg_replace('/\s+/', '-', $slug);
                    
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE portfolio_galleries 
                            SET name = ?, slug = ?, description = ?, category = ?, client_name = ?, 
                                project_date = ?, project_url = ?, tags = ?, status = ?, featured = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        
                        if ($stmt->execute([$name, $slug, $description, $category, $client_name, $project_date, $project_url, json_encode($tags), $status, $featured, $galleryId])) {
                            $success = 'Gallery updated successfully!';
                        } else {
                            $error = 'Failed to update gallery.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete_gallery':
                $galleryId = intval($_POST['gallery_id'] ?? 0);
                
                try {
                    // First, delete all images in the gallery
                    $stmt = $pdo->prepare("SELECT filename FROM portfolio_images WHERE gallery_id = ?");
                    $stmt->execute([$galleryId]);
                    $images = $stmt->fetchAll();
                    
                    // Delete image files
                    foreach ($images as $image) {
                        $imagePath = BASE_PATH . 'assets/images/portfolio/' . $image['filename'];
                        $thumbPath = BASE_PATH . 'assets/images/portfolio/thumbnails/' . $image['filename'];
                        if (file_exists($imagePath)) unlink($imagePath);
                        if (file_exists($thumbPath)) unlink($thumbPath);
                    }
                    
                    // Delete gallery (images will be deleted by CASCADE)
                    $stmt = $pdo->prepare("DELETE FROM portfolio_galleries WHERE id = ?");
                    
                    if ($stmt->execute([$galleryId])) {
                        $success = 'Gallery and all its images deleted successfully!';
                    } else {
                        $error = 'Failed to delete gallery.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'bulk_action':
                $selectedGalleries = $_POST['selected_galleries'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';

                if (empty($selectedGalleries) || empty($bulkAction)) {
                    $error = 'Please select galleries and an action.';
                } else {
                    $updatedCount = 0;
                    
                    try {
                        foreach ($selectedGalleries as $galleryId) {
                            switch ($bulkAction) {
                                case 'activate':
                                    $stmt = $pdo->prepare("UPDATE portfolio_galleries SET status = 'active', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($galleryId)])) $updatedCount++;
                                    break;
                                case 'deactivate':
                                    $stmt = $pdo->prepare("UPDATE portfolio_galleries SET status = 'inactive', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($galleryId)])) $updatedCount++;
                                    break;
                                case 'feature':
                                    $stmt = $pdo->prepare("UPDATE portfolio_galleries SET featured = TRUE, updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($galleryId)])) $updatedCount++;
                                    break;
                                case 'unfeature':
                                    $stmt = $pdo->prepare("UPDATE portfolio_galleries SET featured = FALSE, updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($galleryId)])) $updatedCount++;
                                    break;
                                case 'delete':
                                    // Delete images first
                                    $stmt = $pdo->prepare("SELECT filename FROM portfolio_images WHERE gallery_id = ?");
                                    $stmt->execute([intval($galleryId)]);
                                    $images = $stmt->fetchAll();
                                    
                                    foreach ($images as $image) {
                                        $imagePath = BASE_PATH . 'assets/images/portfolio/' . $image['filename'];
                                        $thumbPath = BASE_PATH . 'assets/images/portfolio/thumbnails/' . $image['filename'];
                                        if (file_exists($imagePath)) unlink($imagePath);
                                        if (file_exists($thumbPath)) unlink($thumbPath);
                                    }
                                    
                                    $stmt = $pdo->prepare("DELETE FROM portfolio_galleries WHERE id = ?");
                                    if ($stmt->execute([intval($galleryId)])) $updatedCount++;
                                    break;
                            }
                        }
                        
                        $success = "Bulk action completed successfully! {$updatedCount} galleries updated.";
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Get galleries from database
try {
    $stmt = $pdo->query("
        SELECT g.*, 
               (SELECT COUNT(*) FROM portfolio_images WHERE gallery_id = g.id) as image_count,
               (SELECT filename FROM portfolio_images WHERE gallery_id = g.id AND is_cover = 1 LIMIT 1) as cover_image
        FROM portfolio_galleries g 
        ORDER BY g.featured DESC, g.sort_order ASC, g.created_at DESC
    ");
    $galleries = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $galleries = [];
    $error = 'Failed to load galleries: ' . $e->getMessage();
}

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM portfolio_categories WHERE status = 'active' ORDER BY sort_order, name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $categories = [];
}

// Apply filters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');
$category_filter = $_GET['category'] ?? '';

$filteredGalleries = $galleries;

// Apply search filter
if (!empty($search)) {
    $filteredGalleries = array_filter($filteredGalleries, function($gallery) use ($search) {
        return stripos($gallery['name'], $search) !== false ||
               stripos($gallery['description'], $search) !== false ||
               stripos($gallery['client_name'], $search) !== false;
    });
}

// Apply category filter
if (!empty($category_filter)) {
    $filteredGalleries = array_filter($filteredGalleries, function($gallery) use ($category_filter) {
        return $gallery['category'] === $category_filter;
    });
}

// Apply status filter
switch ($filter) {
    case 'active':
        $filteredGalleries = array_filter($filteredGalleries, function($gallery) {
            return $gallery['status'] === 'active';
        });
        break;
    case 'inactive':
        $filteredGalleries = array_filter($filteredGalleries, function($gallery) {
            return $gallery['status'] === 'inactive';
        });
        break;
    case 'featured':
        $filteredGalleries = array_filter($filteredGalleries, function($gallery) {
            return $gallery['featured'];
        });
        break;
    case 'draft':
        $filteredGalleries = array_filter($filteredGalleries, function($gallery) {
            return $gallery['status'] === 'draft';
        });
        break;
}

// Check if we're editing a specific gallery
$editingGallery = null;
if (isset($_GET['edit_gallery'])) {
    $editGalleryId = intval($_GET['edit_gallery']);
    try {
        $stmt = $pdo->prepare("SELECT * FROM portfolio_galleries WHERE id = ?");
        $stmt->execute([$editGalleryId]);
        $editingGallery = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($editingGallery) {
            // Get images for this gallery
            $stmt = $pdo->prepare("SELECT * FROM portfolio_images WHERE gallery_id = ? ORDER BY sort_order, created_at");
            $stmt->execute([$editGalleryId]);
            $editingGallery['images'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (Exception $e) {
        $error = 'Failed to load gallery: ' . $e->getMessage();
    }
}

$pageTitle = 'Portfolio Management - Admin';
$bodyClass = 'admin-portfolio';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <?php if ($editingGallery): ?>
                <!-- Gallery Edit Mode -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                    <h1 class="h2 text-white">
                        <i class="fas fa-edit me-2"></i>
                        Editing: <?php echo htmlspecialchars($editingGallery['name']); ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="/admin/portfolio.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Galleries
                        </a>
                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#bulkUploadModal">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Bulk Upload Images
                        </button>
                    </div>
                </div>

                <!-- Gallery Edit Form -->
                <div class="row">
                    <div class="col-lg-4">
                        <div class="card bg-dark-grey-1 border-magenta mb-4">
                            <div class="card-header bg-dark-grey-2 border-magenta">
                                <h5 class="mb-0 text-magenta">
                                    <i class="fas fa-cog me-2"></i>Gallery Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="edit_gallery">
                                    <input type="hidden" name="gallery_id" value="<?php echo $editingGallery['id']; ?>">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                                    <div class="mb-3">
                                        <label for="galleryName" class="form-label text-white">Gallery Name *</label>
                                        <input type="text" class="form-control" id="galleryName" name="name"
                                               value="<?php echo htmlspecialchars($editingGallery['name']); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="galleryDescription" class="form-label text-white">Description</label>
                                        <textarea class="form-control" id="galleryDescription" name="description" rows="4"><?php echo htmlspecialchars($editingGallery['description']); ?></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="galleryCategory" class="form-label text-white">Category</label>
                                        <select class="form-select" id="galleryCategory" name="category">
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo htmlspecialchars($category['name']); ?>"
                                                        <?php echo $editingGallery['category'] === $category['name'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="clientName" class="form-label text-white">Client Name</label>
                                        <input type="text" class="form-control" id="clientName" name="client_name"
                                               value="<?php echo htmlspecialchars($editingGallery['client_name']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="projectDate" class="form-label text-white">Project Date</label>
                                        <input type="date" class="form-control" id="projectDate" name="project_date"
                                               value="<?php echo $editingGallery['project_date']; ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="projectUrl" class="form-label text-white">Project URL</label>
                                        <input type="url" class="form-control" id="projectUrl" name="project_url"
                                               value="<?php echo htmlspecialchars($editingGallery['project_url']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="galleryTags" class="form-label text-white">Tags</label>
                                        <input type="text" class="form-control" id="galleryTags" name="tags"
                                               value="<?php echo implode(', ', json_decode($editingGallery['tags'] ?? '[]', true)); ?>"
                                               placeholder="tag1, tag2, tag3">
                                        <div class="form-text text-off-white">Separate tags with commas</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="galleryStatus" class="form-label text-white">Status</label>
                                        <select class="form-select" id="galleryStatus" name="status">
                                            <option value="active" <?php echo $editingGallery['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editingGallery['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                            <option value="draft" <?php echo $editingGallery['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                        </select>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="galleryFeatured" name="featured"
                                               <?php echo $editingGallery['featured'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label text-white" for="galleryFeatured">
                                            Featured Gallery
                                        </label>
                                    </div>

                                    <button type="submit" class="btn btn-magenta w-100">
                                        <i class="fas fa-save me-2"></i>Update Gallery
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Gallery Stats -->
                        <div class="card bg-dark-grey-1 border-cyan">
                            <div class="card-header bg-dark-grey-2 border-cyan">
                                <h5 class="mb-0 text-cyan">
                                    <i class="fas fa-chart-bar me-2"></i>Gallery Stats
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h3 class="text-cyan"><?php echo count($editingGallery['images']); ?></h3>
                                        <small class="text-off-white">Images</small>
                                    </div>
                                    <div class="col-6">
                                        <h3 class="text-yellow"><?php echo $editingGallery['view_count']; ?></h3>
                                        <small class="text-off-white">Views</small>
                                    </div>
                                </div>
                                <hr class="border-dark-grey-3">
                                <div class="text-center">
                                    <small class="text-off-white">
                                        Created: <?php echo date('M j, Y', strtotime($editingGallery['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-8">
                        <!-- Gallery Images -->
                        <div class="card bg-dark-grey-1 border-yellow">
                            <div class="card-header bg-dark-grey-2 border-yellow">
                                <h5 class="mb-0 text-yellow">
                                    <i class="fas fa-images me-2"></i>
                                    Gallery Images (<?php echo count($editingGallery['images']); ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($editingGallery['images'])): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-images fa-3x text-off-white mb-3"></i>
                                        <h5 class="text-white">No images in this gallery</h5>
                                        <p class="text-off-white">Upload images to get started.</p>
                                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#bulkUploadModal">
                                            <i class="fas fa-cloud-upload-alt me-2"></i>Upload Images
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="row g-3" id="galleryImages">
                                        <?php foreach ($editingGallery['images'] as $image): ?>
                                            <div class="col-md-4 col-lg-3">
                                                <div class="card bg-dark-grey-2 border-0 image-card" data-image-id="<?php echo $image['id']; ?>">
                                                    <div class="position-relative">
                                                        <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/thumbnails/<?php echo htmlspecialchars($image['filename']); ?>"
                                                             alt="<?php echo htmlspecialchars($image['alt_text']); ?>"
                                                             class="card-img-top" style="height: 150px; object-fit: cover;"
                                                             onerror="this.src='<?php echo SITE_URL; ?>/assets/images/placeholder.jpg'">

                                                        <?php if ($image['is_cover']): ?>
                                                            <span class="position-absolute top-0 start-0 badge bg-magenta m-2">
                                                                <i class="fas fa-star me-1"></i>Cover
                                                            </span>
                                                        <?php endif; ?>

                                                        <div class="position-absolute top-0 end-0 m-2">
                                                            <div class="btn-group-vertical" role="group">
                                                                <button class="btn btn-sm btn-outline-light" onclick="editImage(<?php echo $image['id']; ?>)" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteImage(<?php echo $image['id']; ?>)" title="Delete">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-body p-2">
                                                        <h6 class="card-title text-white mb-1" style="font-size: 0.8rem;">
                                                            <?php echo htmlspecialchars($image['title'] ?: $image['original_filename']); ?>
                                                        </h6>
                                                        <small class="text-off-white">
                                                            <?php echo $image['dimensions']; ?> •
                                                            <?php echo round($image['file_size'] / 1024, 1); ?>KB
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <div class="mt-4 text-center">
                                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#bulkUploadModal">
                                            <i class="fas fa-plus me-2"></i>Add More Images
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

            <?php else: ?>
                <!-- Gallery List Mode -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                    <h1 class="h2 text-white">Portfolio Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#galleryModal">
                            <i class="fas fa-plus me-2"></i>Create Gallery
                        </button>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if ($success): ?>
                    <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Filters and Search -->
                <div class="card bg-dark-grey-1 border-cyan mb-4">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-filter me-2"></i>Filter Galleries
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="searchGalleries" class="form-label text-white">Search</label>
                                <input type="text" class="form-control" id="searchGalleries" name="search"
                                       placeholder="Search galleries..." value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="filterCategory" class="form-label text-white">Category</label>
                                <select class="form-select" id="filterCategory" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['name']); ?>"
                                                <?php echo $category_filter === $category['name'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterStatus" class="form-label text-white">Status</label>
                                <select class="form-select" id="filterStatus" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Galleries</option>
                                    <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active Only</option>
                                    <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>Inactive Only</option>
                                    <option value="featured" <?php echo $filter === 'featured' ? 'selected' : ''; ?>>Featured Only</option>
                                    <option value="draft" <?php echo $filter === 'draft' ? 'selected' : ''; ?>>Draft Only</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-white">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-cyan">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                    <a href="/admin/portfolio.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Bulk Operations -->
                <div class="card bg-dark-grey-1 border-yellow mb-4">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-tasks me-2"></i>Bulk Operations
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="bulkForm">
                            <input type="hidden" name="action" value="bulk_action">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label text-white">Select Action</label>
                                    <select class="form-select" name="bulk_action" required>
                                        <option value="">Choose action...</option>
                                        <option value="activate">Activate Selected</option>
                                        <option value="deactivate">Deactivate Selected</option>
                                        <option value="feature">Feature Selected</option>
                                        <option value="unfeature">Unfeature Selected</option>
                                        <option value="delete">Delete Selected</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-cyan" onclick="selectAllGalleries()">
                                        <i class="fas fa-check-square me-2"></i>Select All
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="clearSelection()">
                                        <i class="fas fa-square me-2"></i>Clear All
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-yellow text-black" onclick="return confirmBulkAction()">
                                        <i class="fas fa-play me-2"></i>Execute Action
                                    </button>
                                    <span class="text-off-white ms-2">
                                        <span id="selectedCount">0</span> selected
                                    </span>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Galleries Grid -->
                <div class="card bg-dark-grey-1 border-magenta">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-images me-2"></i>
                            Portfolio Galleries (<?php echo count($filteredGalleries); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($filteredGalleries)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-images fa-3x text-off-white mb-3"></i>
                                <h5 class="text-white">No galleries found</h5>
                                <p class="text-off-white">Create your first portfolio gallery to get started.</p>
                                <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#galleryModal">
                                    <i class="fas fa-plus me-2"></i>Create Gallery
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="row g-4">
                                <?php foreach ($filteredGalleries as $gallery): ?>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card bg-dark-grey-2 border-0 h-100 gallery-card">
                                            <div class="position-relative">
                                                <?php if ($gallery['cover_image']): ?>
                                                    <img src="<?php echo SITE_URL; ?>/assets/images/portfolio/covers/<?php echo htmlspecialchars($gallery['cover_image']); ?>"
                                                         alt="<?php echo htmlspecialchars($gallery['name']); ?>"
                                                         class="card-img-top" style="height: 200px; object-fit: cover;"
                                                         onerror="this.src='<?php echo SITE_URL; ?>/assets/images/placeholder.jpg'">
                                                <?php else: ?>
                                                    <div class="bg-dark-grey-3 d-flex align-items-center justify-content-center" style="height: 200px;">
                                                        <i class="fas fa-images fa-3x text-off-white"></i>
                                                    </div>
                                                <?php endif; ?>

                                                <!-- Gallery badges -->
                                                <div class="position-absolute top-0 start-0 m-2">
                                                    <?php if ($gallery['featured']): ?>
                                                        <span class="badge bg-magenta me-1">
                                                            <i class="fas fa-star me-1"></i>Featured
                                                        </span>
                                                    <?php endif; ?>

                                                    <?php if ($gallery['status'] === 'active'): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif ($gallery['status'] === 'draft'): ?>
                                                        <span class="badge bg-warning text-dark">Draft</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Selection checkbox -->
                                                <div class="position-absolute top-0 end-0 m-2">
                                                    <input type="checkbox" class="form-check-input gallery-select"
                                                           name="selected_galleries[]" value="<?php echo $gallery['id']; ?>"
                                                           onchange="updateSelectedCount()">
                                                </div>
                                            </div>

                                            <div class="card-body">
                                                <h5 class="card-title text-white mb-2">
                                                    <?php echo htmlspecialchars($gallery['name']); ?>
                                                </h5>

                                                <p class="card-text text-off-white small mb-2">
                                                    <?php echo htmlspecialchars(substr($gallery['description'], 0, 100)); ?>
                                                    <?php if (strlen($gallery['description']) > 100): ?>...<?php endif; ?>
                                                </p>

                                                <div class="row text-center mb-3">
                                                    <div class="col-4">
                                                        <small class="text-cyan"><?php echo $gallery['image_count']; ?></small>
                                                        <br><small class="text-off-white">Images</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-yellow"><?php echo $gallery['view_count']; ?></small>
                                                        <br><small class="text-off-white">Views</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <?php if ($gallery['category']): ?>
                                                            <small class="text-magenta"><?php echo htmlspecialchars($gallery['category']); ?></small>
                                                        <?php else: ?>
                                                            <small class="text-off-white">No Category</small>
                                                        <?php endif; ?>
                                                        <br><small class="text-off-white">Category</small>
                                                    </div>
                                                </div>

                                                <div class="d-flex gap-2">
                                                    <a href="/admin/portfolio.php?edit_gallery=<?php echo $gallery['id']; ?>"
                                                       class="btn btn-sm btn-cyan flex-fill">
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteGallery(<?php echo $gallery['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="card-footer bg-dark-grey-3 border-0">
                                                <small class="text-off-white">
                                                    Created: <?php echo date('M j, Y', strtotime($gallery['created_at'])); ?>
                                                    <?php if ($gallery['client_name']): ?>
                                                        • Client: <?php echo htmlspecialchars($gallery['client_name']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
    </div>
</div>

<!-- Gallery Modal -->
<div class="modal fade" id="galleryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta">
                    <i class="fas fa-images me-2"></i>
                    <span id="galleryModalTitle">Create Gallery</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="galleryForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" id="galleryFormAction" value="create_gallery">
                    <input type="hidden" name="gallery_id" id="galleryFormId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="galleryModalName" class="form-label text-white">Gallery Name *</label>
                                <input type="text" class="form-control" id="galleryModalName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="galleryModalCategory" class="form-label text-white">Category</label>
                                <select class="form-select" id="galleryModalCategory" name="category">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="galleryModalDescription" class="form-label text-white">Description</label>
                        <textarea class="form-control" id="galleryModalDescription" name="description" rows="4"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="galleryModalClient" class="form-label text-white">Client Name</label>
                                <input type="text" class="form-control" id="galleryModalClient" name="client_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="galleryModalDate" class="form-label text-white">Project Date</label>
                                <input type="date" class="form-control" id="galleryModalDate" name="project_date">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="galleryModalUrl" class="form-label text-white">Project URL</label>
                        <input type="url" class="form-control" id="galleryModalUrl" name="project_url">
                    </div>

                    <div class="mb-3">
                        <label for="galleryModalTags" class="form-label text-white">Tags</label>
                        <input type="text" class="form-control" id="galleryModalTags" name="tags"
                               placeholder="tag1, tag2, tag3">
                        <div class="form-text text-off-white">Separate tags with commas</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="galleryModalStatus" class="form-label text-white">Status</label>
                                <select class="form-select" id="galleryModalStatus" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="draft">Draft</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="galleryModalFeatured" name="featured">
                                    <label class="form-check-label text-white" for="galleryModalFeatured">
                                        Featured Gallery
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Gallery
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Upload Modal -->
<div class="modal fade" id="bulkUploadModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    Bulk Upload Images
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <!-- Drag & Drop Upload Area -->
                        <div class="upload-area border border-dashed border-cyan rounded p-5 text-center mb-4"
                             id="uploadArea" ondrop="dropHandler(event);" ondragover="dragOverHandler(event);">
                            <i class="fas fa-cloud-upload-alt fa-3x text-cyan mb-3"></i>
                            <h5 class="text-white">Drag & Drop Images Here</h5>
                            <p class="text-off-white">or click to browse files</p>
                            <input type="file" id="fileInput" multiple accept="image/*" style="display: none;" onchange="handleFiles(this.files)">
                            <button type="button" class="btn btn-cyan" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>

                        <!-- Upload Progress -->
                        <div id="uploadProgress" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar bg-cyan" role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>
                            <div class="text-center">
                                <span id="uploadStatus" class="text-white">Uploading...</span>
                            </div>
                        </div>

                        <!-- File Preview -->
                        <div id="filePreview" class="row g-3"></div>
                    </div>

                    <div class="col-md-4">
                        <!-- Upload Settings -->
                        <div class="card bg-dark-grey-2 border-0">
                            <div class="card-header bg-dark-grey-3">
                                <h6 class="mb-0 text-white">Upload Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-white">Image Quality</label>
                                    <select class="form-select" id="imageQuality">
                                        <option value="high">High Quality (Original)</option>
                                        <option value="medium" selected>Medium Quality (Optimized)</option>
                                        <option value="low">Low Quality (Compressed)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-white">Resize Images</label>
                                    <select class="form-select" id="resizeOption">
                                        <option value="none">Keep Original Size</option>
                                        <option value="1920">Max Width: 1920px</option>
                                        <option value="1200" selected>Max Width: 1200px</option>
                                        <option value="800">Max Width: 800px</option>
                                    </select>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="generateThumbnails" checked>
                                    <label class="form-check-label text-white" for="generateThumbnails">
                                        Generate Thumbnails
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoSetCover">
                                    <label class="form-check-label text-white" for="autoSetCover">
                                        Set First Image as Cover
                                    </label>
                                </div>

                                <hr class="border-dark-grey-3">

                                <div class="mb-3">
                                    <label class="form-label text-white">Default Alt Text</label>
                                    <input type="text" class="form-control" id="defaultAltText"
                                           placeholder="Portfolio image">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-white">Add Tags</label>
                                    <input type="text" class="form-control" id="bulkTags"
                                           placeholder="tag1, tag2, tag3">
                                    <div class="form-text text-off-white">Applied to all uploaded images</div>
                                </div>
                            </div>
                        </div>

                        <!-- Upload Stats -->
                        <div class="card bg-dark-grey-2 border-0 mt-3">
                            <div class="card-body text-center">
                                <div class="row">
                                    <div class="col-6">
                                        <h4 class="text-cyan" id="selectedCount">0</h4>
                                        <small class="text-off-white">Selected</small>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-yellow" id="uploadedCount">0</h4>
                                        <small class="text-off-white">Uploaded</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-dark-grey-2 border-cyan">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-success" id="startUpload" onclick="startBulkUpload()" disabled>
                    <i class="fas fa-upload me-2"></i>Start Upload
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Portfolio management functions
let selectedFiles = [];
let currentGalleryId = <?php echo $editingGallery ? $editingGallery['id'] : 'null'; ?>;

// Gallery management
function deleteGallery(galleryId) {
    if (confirm('Are you sure you want to delete this gallery and all its images? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_gallery">
            <input type="hidden" name="gallery_id" value="${galleryId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk operations
function selectAllGalleries() {
    const checkboxes = document.querySelectorAll('.gallery-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.gallery-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.gallery-select:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

function confirmBulkAction() {
    const selectedCheckboxes = document.querySelectorAll('.gallery-select:checked');
    const action = document.querySelector('select[name="bulk_action"]').value;

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one gallery.');
        return false;
    }

    if (!action) {
        alert('Please select an action.');
        return false;
    }

    const actionText = action === 'delete' ? 'delete' : action;
    const confirmMessage = `Are you sure you want to ${actionText} ${selectedCheckboxes.length} selected galleries?`;

    if (action === 'delete') {
        return confirm(confirmMessage + ' This action cannot be undone.');
    }

    return confirm(confirmMessage);
}

// Add selected galleries to bulk form before submission
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    const selectedCheckboxes = document.querySelectorAll('.gallery-select:checked');

    // Remove existing hidden inputs
    const existingInputs = this.querySelectorAll('input[name="selected_galleries[]"]');
    existingInputs.forEach(input => input.remove());

    // Add selected galleries as hidden inputs
    selectedCheckboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'selected_galleries[]';
        hiddenInput.value = checkbox.value;
        this.appendChild(hiddenInput);
    });
});

// Reset gallery modal when hidden
document.getElementById('galleryModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('galleryForm').reset();
    document.getElementById('galleryModalTitle').textContent = 'Create Gallery';
    document.getElementById('galleryFormAction').value = 'create_gallery';
    document.getElementById('galleryFormId').value = '';
});

// File upload functions
function dragOverHandler(ev) {
    ev.preventDefault();
    ev.currentTarget.classList.add('border-magenta');
}

function dropHandler(ev) {
    ev.preventDefault();
    ev.currentTarget.classList.remove('border-magenta');

    const files = ev.dataTransfer.files;
    handleFiles(files);
}

function handleFiles(files) {
    selectedFiles = Array.from(files).filter(file => file.type.startsWith('image/'));

    if (selectedFiles.length === 0) {
        alert('Please select valid image files.');
        return;
    }

    displayFilePreview();
    updateUploadStats();
    document.getElementById('startUpload').disabled = false;
}

function displayFilePreview() {
    const preview = document.getElementById('filePreview');
    preview.innerHTML = '';

    selectedFiles.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const div = document.createElement('div');
            div.className = 'col-md-4 col-lg-3';
            div.innerHTML = `
                <div class="card bg-dark-grey-2 border-0">
                    <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                    <div class="card-body p-2">
                        <h6 class="card-title text-white mb-1" style="font-size: 0.8rem;">${file.name}</h6>
                        <small class="text-off-white">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                        <button class="btn btn-sm btn-outline-danger float-end" onclick="removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            preview.appendChild(div);
        };
        reader.readAsDataURL(file);
    });
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    displayFilePreview();
    updateUploadStats();

    if (selectedFiles.length === 0) {
        document.getElementById('startUpload').disabled = true;
    }
}

function updateUploadStats() {
    document.getElementById('selectedCount').textContent = selectedFiles.length;
}

function startBulkUpload() {
    if (!currentGalleryId) {
        alert('Please create a gallery first before uploading images.');
        return;
    }

    if (selectedFiles.length === 0) {
        alert('Please select files to upload.');
        return;
    }

    document.getElementById('uploadProgress').style.display = 'block';
    document.getElementById('startUpload').disabled = true;

    uploadFiles();
}

async function uploadFiles() {
    const totalFiles = selectedFiles.length;
    let uploadedCount = 0;

    for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        const formData = new FormData();

        formData.append('file', file);
        formData.append('gallery_id', currentGalleryId);
        formData.append('quality', document.getElementById('imageQuality').value);
        formData.append('resize', document.getElementById('resizeOption').value);
        formData.append('generate_thumbnails', document.getElementById('generateThumbnails').checked);
        formData.append('auto_set_cover', document.getElementById('autoSetCover').checked && i === 0);
        formData.append('alt_text', document.getElementById('defaultAltText').value || 'Portfolio image');
        formData.append('tags', document.getElementById('bulkTags').value);
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

        try {
            const response = await fetch('/admin/ajax/upload-portfolio-image.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                uploadedCount++;
                document.getElementById('uploadedCount').textContent = uploadedCount;

                const progress = (uploadedCount / totalFiles) * 100;
                document.getElementById('progressBar').style.width = progress + '%';
                document.getElementById('uploadStatus').textContent = `Uploaded ${uploadedCount} of ${totalFiles} files`;
            } else {
                console.error('Upload failed:', result.message);
            }
        } catch (error) {
            console.error('Upload error:', error);
        }
    }

    // Upload complete
    document.getElementById('uploadStatus').textContent = `Upload complete! ${uploadedCount} files uploaded.`;

    setTimeout(() => {
        // Refresh the page to show uploaded images
        window.location.reload();
    }, 2000);
}

// Image management functions
function editImage(imageId) {
    // TODO: Implement image editing modal
    console.log('Edit image:', imageId);
}

function deleteImage(imageId) {
    if (confirm('Are you sure you want to delete this image?')) {
        // TODO: Implement image deletion
        console.log('Delete image:', imageId);
    }
}

// Initialize drag and drop
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea) {
        uploadArea.addEventListener('dragenter', function(e) {
            e.preventDefault();
            this.classList.add('border-magenta');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            if (!this.contains(e.relatedTarget)) {
                this.classList.remove('border-magenta');
            }
        });
    }
});
</script>


