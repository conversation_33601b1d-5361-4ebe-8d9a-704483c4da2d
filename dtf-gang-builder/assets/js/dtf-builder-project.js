/**
 * DTF Gang Builder - Project Management
 * Save and load project functionality with modal dialogs
 */

// Extend the ProfessionalDTFBuilder class with project management methods
Object.assign(ProfessionalDTFBuilder.prototype, {

    saveProject() {
        console.log('💾 Opening save project modal...');
        
        // Update modal stats
        document.getElementById('save-modal-images').textContent = this.images.length;
        document.getElementById('save-modal-copies').textContent = this.imagePositions.length;
        document.getElementById('save-modal-sheet').textContent = this.sheetSize.replace('x', '" × ') + '"';
        
        // Generate default project name
        const now = new Date();
        const defaultName = `DTF Project ${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')}`;
        document.getElementById('project-name-input').value = defaultName;
        
        // Show modal
        document.getElementById('save-project-modal').classList.remove('hidden');
    },

    loadProject() {
        console.log('📂 Opening load project modal...');
        
        // Clear previous data
        document.getElementById('project-file-input').value = '';
        document.getElementById('project-data-input').value = '';
        document.getElementById('load-project-preview').classList.add('hidden');
        document.getElementById('load-project-confirm-btn').disabled = true;
        
        // Show modal
        document.getElementById('load-project-modal').classList.remove('hidden');
    },

    generateProjectData() {
        const projectData = {
            name: document.getElementById('project-name-input').value || 'Untitled Project',
            description: document.getElementById('project-description-input').value || '',
            version: '1.0',
            created: new Date().toISOString(),
            settings: {
                sheetSize: this.sheetSize,
                dpi: this.dpi,
                spacing: this.spacing,
                bleed: this.bleed,
                autoRotate: this.autoRotate,
                maintainAspect: this.maintainAspect,
                addMargins: this.addMargins,
                gridSize: this.gridSize,
                gridColor: this.gridColor,
                showGrid: this.showGrid,
                snapToGrid: this.snapToGrid
            },
            images: this.images.map(img => ({
                name: img.name,
                width: img.width,
                height: img.height,
                physicalWidth: img.physicalWidth,
                physicalHeight: img.physicalHeight,
                dpi: img.dpi,
                maxCopies: img.maxCopies,
                // Try to preserve image data if available
                dataUrl: img.dataUrl || img.src || null,
                // Add metadata for better reconstruction
                originalSize: img.originalSize || null,
                fileType: img.type || null,
                uploadedAt: img.uploadedAt || new Date().toISOString()
            })),
            layout: this.imagePositions.map(pos => ({
                x: pos.x,
                y: pos.y,
                width: pos.width,
                height: pos.height,
                imageIndex: pos.imageIndex,
                rotation: pos.rotation || 0
            })),
            stats: {
                totalImages: this.images.length,
                totalCopies: this.imagePositions.length,
                efficiency: this.calculateEfficiency()
            }
        };

        return projectData;
    },

    calculateEfficiency() {
        const dims = this.getSheetDimensions();
        const totalSheetArea = dims.realWidth * dims.realHeight;
        
        let usedArea = 0;
        this.imagePositions.forEach(pos => {
            const widthInches = pos.width / dims.scale;
            const heightInches = pos.height / dims.scale;
            usedArea += widthInches * heightInches;
        });
        
        return totalSheetArea > 0 ? Math.round((usedArea / totalSheetArea) * 100) : 0;
    },

    downloadProjectFile() {
        const projectData = this.generateProjectData();
        const jsonString = JSON.stringify(projectData, null, 2);
        
        // Create download
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${projectData.name.replace(/[^a-z0-9]/gi, '_')}.dtf`;
        a.click();
        URL.revokeObjectURL(url);
        
        // Save to recent projects
        this.saveToRecentProjects(projectData);
        
        this.showStatus(`Project "${projectData.name}" saved successfully!`, 'success');
        console.log('💾 Project saved:', projectData.name);
    },

    saveToRecentProjects(projectData) {
        let recentProjects = JSON.parse(localStorage.getItem('dtf_recent_projects') || '[]');
        
        // Remove existing project with same name
        recentProjects = recentProjects.filter(p => p.name !== projectData.name);
        
        // Add to beginning
        recentProjects.unshift({
            name: projectData.name,
            description: projectData.description,
            created: projectData.created,
            stats: projectData.stats
        });
        
        // Keep only last 10 projects
        recentProjects = recentProjects.slice(0, 10);
        
        localStorage.setItem('dtf_recent_projects', JSON.stringify(recentProjects));
        this.updateRecentProjectsList();
    },

    updateRecentProjectsList() {
        const recentProjects = JSON.parse(localStorage.getItem('dtf_recent_projects') || '[]');
        const listContainer = document.getElementById('recent-projects-list');
        
        if (recentProjects.length === 0) {
            listContainer.innerHTML = '<div class="no-projects">No recent projects</div>';
            return;
        }
        
        listContainer.innerHTML = recentProjects.map(project => `
            <div class="project-item" onclick="window.dtfBuilder.loadRecentProject('${project.name}')">
                <div class="project-name">${project.name}</div>
                <div class="project-meta">
                    <span>${project.stats.totalImages} images, ${project.stats.totalCopies} copies</span>
                    <span>${new Date(project.created).toLocaleDateString()}</span>
                </div>
            </div>
        `).join('');
    },

    loadRecentProject(projectName) {
        // In a real implementation, you'd load the full project data
        // For now, just show a message
        this.showStatus(`Loading recent project "${projectName}" - Feature coming soon!`, 'info');
    },

    validateProjectData(data) {
        try {
            const parsed = typeof data === 'string' ? JSON.parse(data) : data;

            // Check for required fields with detailed error messages
            const errors = [];

            if (!parsed.name) {
                errors.push('Project name is required');
            }

            if (!parsed.settings) {
                errors.push('Project settings are required');
            }

            // Images and layout are optional but should be arrays if present
            if (parsed.images && !Array.isArray(parsed.images)) {
                errors.push('Images must be an array');
            }

            if (parsed.layout && !Array.isArray(parsed.layout)) {
                errors.push('Layout must be an array');
            }

            if (errors.length > 0) {
                return { valid: false, error: errors.join(', ') };
            }

            // Add default values for missing optional fields
            const validatedData = {
                ...parsed,
                images: parsed.images || [],
                layout: parsed.layout || [],
                version: parsed.version || '1.0',
                created: parsed.created || new Date().toISOString(),
                description: parsed.description || ''
            };

            return { valid: true, data: validatedData };
        } catch (error) {
            return { valid: false, error: `Invalid JSON format: ${error.message}` };
        }
    },

    previewProjectData(data) {
        const validation = this.validateProjectData(data);

        if (!validation.valid) {
            document.getElementById('load-project-preview').classList.add('hidden');
            document.getElementById('load-project-confirm-btn').disabled = true;
            this.showStatus(validation.error, 'error');
            return;
        }

        const project = validation.data;

        // Update preview with enhanced information
        document.getElementById('preview-name').textContent = project.name;
        document.getElementById('preview-images').textContent = project.images.length;
        document.getElementById('preview-copies').textContent = project.layout.length;
        document.getElementById('preview-created').textContent = new Date(project.created).toLocaleString();

        // Add additional preview information if elements exist
        const previewDescription = document.getElementById('preview-description');
        if (previewDescription) {
            previewDescription.textContent = project.description || 'No description';
        }

        const previewVersion = document.getElementById('preview-version');
        if (previewVersion) {
            previewVersion.textContent = project.version || '1.0';
        }

        const previewSheetSize = document.getElementById('preview-sheet-size');
        if (previewSheetSize && project.settings) {
            previewSheetSize.textContent = project.settings.sheetSize || 'Not specified';
        }

        // Count images with data vs placeholders
        const imagesWithData = project.images.filter(img => img.dataUrl).length;
        const previewImageStatus = document.getElementById('preview-image-status');
        if (previewImageStatus) {
            if (project.images.length === 0) {
                previewImageStatus.textContent = 'No images';
                previewImageStatus.className = 'status-neutral';
            } else if (imagesWithData === project.images.length) {
                previewImageStatus.textContent = 'All images preserved';
                previewImageStatus.className = 'status-success';
            } else if (imagesWithData > 0) {
                previewImageStatus.textContent = `${imagesWithData}/${project.images.length} images preserved`;
                previewImageStatus.className = 'status-warning';
            } else {
                previewImageStatus.textContent = 'Images need re-upload';
                previewImageStatus.className = 'status-warning';
            }
        }

        document.getElementById('load-project-preview').classList.remove('hidden');
        document.getElementById('load-project-confirm-btn').disabled = false;

        // Clear any previous error messages
        this.showStatus('Project preview loaded successfully', 'success');
    },

    loadProjectData() {
        const fileInput = document.getElementById('project-file-input');
        const textInput = document.getElementById('project-data-input');
        
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const validation = this.validateProjectData(e.target.result);
                if (validation.valid) {
                    this.applyProjectData(validation.data);
                } else {
                    this.showStatus(validation.error, 'error');
                }
            };
            
            reader.readAsText(file);
        } else if (textInput.value.trim()) {
            const validation = this.validateProjectData(textInput.value);
            if (validation.valid) {
                this.applyProjectData(validation.data);
            } else {
                this.showStatus(validation.error, 'error');
            }
        }
    },

    applyProjectData(projectData) {
        console.log('📂 Loading project:', projectData.name);

        // Clear current project
        this.images = [];
        this.imagePositions = [];
        this.selectedObject = null;

        // Apply settings
        this.sheetSize = projectData.settings.sheetSize || '30x72';
        this.dpi = projectData.settings.dpi || 300;
        this.spacing = projectData.settings.spacing || 0.125;
        this.bleed = projectData.settings.bleed || 0.0625;

        // Apply additional settings if available
        if (projectData.settings.autoRotate !== undefined) this.autoRotate = projectData.settings.autoRotate;
        if (projectData.settings.maintainAspect !== undefined) this.maintainAspect = projectData.settings.maintainAspect;
        if (projectData.settings.addMargins !== undefined) this.addMargins = projectData.settings.addMargins;
        if (projectData.settings.gridSize !== undefined) this.gridSize = projectData.settings.gridSize;
        if (projectData.settings.gridColor !== undefined) this.gridColor = projectData.settings.gridColor;
        if (projectData.settings.showGrid !== undefined) this.showGrid = projectData.settings.showGrid;
        if (projectData.settings.snapToGrid !== undefined) this.snapToGrid = projectData.settings.snapToGrid;

        // Update UI controls
        const sheetSizeSelect = document.getElementById('sheet-size');
        if (sheetSizeSelect) sheetSizeSelect.value = this.sheetSize;

        const dpiSelect = document.getElementById('dpi');
        if (dpiSelect) dpiSelect.value = this.dpi;

        // Try to restore images if data is available
        let restoredImages = 0;
        let totalImages = projectData.images.length;

        if (projectData.images && projectData.images.length > 0) {
            projectData.images.forEach((imgData, index) => {
                if (imgData.dataUrl) {
                    // Try to restore image from data URL
                    const img = new Image();
                    img.onload = () => {
                        const imageObj = {
                            name: imgData.name,
                            width: imgData.width,
                            height: imgData.height,
                            physicalWidth: imgData.physicalWidth,
                            physicalHeight: imgData.physicalHeight,
                            dpi: imgData.dpi,
                            maxCopies: imgData.maxCopies,
                            dataUrl: imgData.dataUrl,
                            src: imgData.dataUrl,
                            element: img
                        };
                        this.images.push(imageObj);
                        restoredImages++;

                        // Update UI when all images are processed
                        if (restoredImages === totalImages) {
                            this.updateImageList();
                            this.updateButtonStates();
                        }
                    };
                    img.src = imgData.dataUrl;
                } else {
                    // Create placeholder for missing image data
                    const placeholderImg = {
                        name: imgData.name,
                        width: imgData.width || 100,
                        height: imgData.height || 100,
                        physicalWidth: imgData.physicalWidth || 1,
                        physicalHeight: imgData.physicalHeight || 1,
                        dpi: imgData.dpi || 300,
                        maxCopies: imgData.maxCopies || 1,
                        dataUrl: null,
                        isPlaceholder: true
                    };
                    this.images.push(placeholderImg);
                }
            });
        }

        // Restore layout positions if available
        if (projectData.layout && projectData.layout.length > 0) {
            this.imagePositions = projectData.layout.map(pos => ({
                x: pos.x,
                y: pos.y,
                width: pos.width,
                height: pos.height,
                imageIndex: pos.imageIndex,
                rotation: pos.rotation || 0
            }));
        }

        this.updateCanvas();
        this.updateStats();
        this.updateImageList();
        this.updateButtonStates();
        this.drawWithImages();

        // Provide detailed feedback
        let statusMessage = `Project "${projectData.name}" loaded successfully!`;
        if (totalImages > 0) {
            if (restoredImages === totalImages) {
                statusMessage += ` All ${totalImages} images restored.`;
            } else if (restoredImages > 0) {
                statusMessage += ` ${restoredImages}/${totalImages} images restored. ${totalImages - restoredImages} need re-upload.`;
            } else {
                statusMessage += ` ${totalImages} image placeholders created. Images need to be re-uploaded.`;
            }
        }

        this.showStatus(statusMessage, restoredImages === totalImages ? 'success' : 'info');

        // Close modal
        document.getElementById('load-project-modal').classList.add('hidden');

        // Prompt for missing image re-upload after a short delay
        if (totalImages > restoredImages) {
            setTimeout(() => {
                this.showImageReuploadPrompt();
            }, 1500);
        }
    },

    // Helper function to handle missing image re-upload
    showImageReuploadPrompt() {
        const missingImages = this.images.filter(img => img.isPlaceholder);
        if (missingImages.length === 0) return;

        const promptMessage = `${missingImages.length} images need to be re-uploaded:\n${missingImages.map(img => `• ${img.name}`).join('\n')}\n\nWould you like to upload them now?`;

        if (confirm(promptMessage)) {
            // Trigger file upload for missing images
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.accept = 'image/*';
            fileInput.onchange = (e) => {
                this.handleMissingImageUpload(e.target.files, missingImages);
            };
            fileInput.click();
        }
    },

    // Handle upload of missing images
    handleMissingImageUpload(files, missingImages) {
        if (files.length === 0) return;

        Array.from(files).forEach((file, index) => {
            if (index < missingImages.length) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // Update the placeholder with actual image data
                        const placeholderIndex = this.images.findIndex(img => img === missingImages[index]);
                        if (placeholderIndex !== -1) {
                            this.images[placeholderIndex] = {
                                ...missingImages[index],
                                dataUrl: e.target.result,
                                src: e.target.result,
                                element: img,
                                isPlaceholder: false
                            };

                            this.updateImageList();
                            this.updateButtonStates();
                            this.drawWithImages();

                            this.showStatus(`Image "${file.name}" restored successfully!`, 'success');
                        }
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }

});

// Global modal functions
function closeSaveModal() {
    document.getElementById('save-project-modal').classList.add('hidden');
}

function closeLoadModal() {
    document.getElementById('load-project-modal').classList.add('hidden');
}

function saveProject() {
    if (window.dtfBuilder) {
        window.dtfBuilder.downloadProjectFile();
        closeSaveModal();
    }
}

function loadProject() {
    if (window.dtfBuilder) {
        window.dtfBuilder.loadProjectData();
    }
}

// Setup event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // File input change handler
    const fileInput = document.getElementById('project-file-input');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0 && window.dtfBuilder) {
                const file = this.files[0];
                const reader = new FileReader();
                reader.onload = (e) => {
                    window.dtfBuilder.previewProjectData(e.target.result);
                };
                reader.readAsText(file);
            }
        });
    }
    
    // Text input change handler
    const textInput = document.getElementById('project-data-input');
    if (textInput) {
        textInput.addEventListener('input', function() {
            if (this.value.trim() && window.dtfBuilder) {
                window.dtfBuilder.previewProjectData(this.value);
            }
        });
    }
    
    // Update recent projects list
    setTimeout(() => {
        if (window.dtfBuilder) {
            window.dtfBuilder.updateRecentProjectsList();
        }
    }, 1000);
});
