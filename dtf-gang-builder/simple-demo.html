<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DTF Gang Builder - Simple Demo</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 600px;
        }

        .sidebar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .canvas-area {
            background: #ffffff;
            border-radius: 10px;
            padding: 20px;
            border: 2px dashed #ddd;
            position: relative;
        }

        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: #e6f3ff;
            border-color: #0056b3;
        }

        .upload-area.dragover {
            background: #cce7ff;
            border-color: #004085;
        }

        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .control-group h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        #canvas-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
            display: inline-block;
        }

        .canvas-controls {
            margin-top: 15px;
            text-align: center;
        }

        .hidden {
            display: none;
        }

        .image-info {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            border: 1px solid #e9ecef;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 3px 0;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .size-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .size-input-group input {
            flex: 1;
        }

        .lock-icon {
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            background: #f8f9fa;
            border: 1px solid #ced4da;
            color: #495057;
        }

        .lock-icon.locked {
            background: #007bff;
            color: white;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 DTF Gang Builder</h1>
            <p>Professional DTF Gang Sheet Creation Tool - Simple Demo</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <!-- Upload Section -->
                <div class="control-group">
                    <h3>📁 Upload Images</h3>
                    <div class="upload-area" id="uploadArea">
                        <div>
                            <strong>📤 Drag & Drop Images</strong><br>
                            or click to browse<br>
                            <small>PNG, JPG, GIF, WebP (Max 10MB each)</small>
                        </div>
                        <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
                    </div>
                </div>

                <!-- Status Section -->
                <div id="statusBox" class="status-box status-info">
                    🔵 Upload an image to get started
                </div>

                <!-- Image Info Section -->
                <div id="imageInfoSection" class="control-group" style="display: none;">
                    <h3>📏 Selected Image Info</h3>
                    <div id="imageInfo" class="image-info">
                        <div class="info-row">
                            <span class="info-label">File:</span>
                            <span id="imageFileName">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Original Size:</span>
                            <span id="imageOriginalSize">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Current Size:</span>
                            <span id="imageCurrentSize">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">DPI:</span>
                            <span id="imageDPI">300</span>
                        </div>
                    </div>
                </div>

                <!-- Image Size Controls -->
                <div id="sizeControlsSection" class="control-group" style="display: none;">
                    <h3>📐 Size Controls</h3>
                    <div class="form-group">
                        <label for="imageWidth">Width (inches):</label>
                        <input type="number" id="imageWidth" class="form-control" step="0.1" min="0.1" max="30">
                    </div>
                    <div class="form-group">
                        <label for="imageHeight">Height (inches):</label>
                        <input type="number" id="imageHeight" class="form-control" step="0.1" min="0.1" max="120">
                    </div>
                    <div class="form-group">
                        <label for="imageDPIControl">DPI:</label>
                        <select id="imageDPIControl" class="form-control">
                            <option value="150">150 DPI (Draft)</option>
                            <option value="300" selected>300 DPI (Standard)</option>
                            <option value="600">600 DPI (High Quality)</option>
                            <option value="1200">1200 DPI (Ultra High)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="maintainAspectRatio" checked> Maintain Aspect Ratio
                        </label>
                    </div>
                    <button id="applySizeBtn" class="btn btn-primary" style="width: 100%;">
                        ✅ Apply Size Changes
                    </button>
                    <button id="deleteImageBtn" class="btn btn-danger" style="width: 100%; margin-top: 10px;">
                        🗑️ Delete Selected Image
                    </button>
                </div>

                <!-- Duplication Section -->
                <div class="control-group">
                    <h3>🔄 Mass Duplication</h3>
                    <div class="form-group">
                        <label for="quantitySelect">Quantity:</label>
                        <select id="quantitySelect" class="form-control">
                            <option value="5">5 copies</option>
                            <option value="10">10 copies</option>
                            <option value="20">20 copies</option>
                            <option value="25">25 copies</option>
                            <option value="50" selected>50 copies</option>
                            <option value="75">75 copies</option>
                            <option value="100">100 copies</option>
                            <option value="150">150 copies</option>
                            <option value="200">200 copies</option>
                            <option value="250">250 copies</option>
                            <option value="300">300 copies</option>
                            <option value="500">500 copies</option>
                        </select>
                    </div>
                    <button id="duplicateBtn" class="btn btn-success" style="width: 100%;">
                        ✨ Create Selected Quantity
                    </button>
                    <button id="fillSheetBtn" class="btn btn-warning" style="width: 100%;">
                        📋 Fill Entire Sheet
                    </button>
                </div>

                <!-- Sheet Settings -->
                <div class="control-group">
                    <h3>📐 Sheet Settings</h3>
                    <div class="form-group">
                        <label for="sheetSize">Sheet Size:</label>
                        <select id="sheetSize" class="form-control">
                            <option value="30x12">30" × 12"</option>
                            <option value="30x24">30" × 24"</option>
                            <option value="30x36">30" × 36"</option>
                            <option value="30x48">30" × 48"</option>
                            <option value="30x60">30" × 60"</option>
                            <option value="30x72" selected>30" × 72"</option>
                            <option value="30x100">30" × 100"</option>
                            <option value="30x120">30" × 120"</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="spacing">Spacing (mm):</label>
                        <input type="number" id="spacing" class="form-control" value="3" min="0" max="20">
                    </div>
                    <div class="form-group">
                        <label for="margins">Margins (mm):</label>
                        <input type="number" id="margins" class="form-control" value="5" min="0" max="50">
                    </div>
                </div>

                <!-- Tools -->
                <div class="control-group">
                    <h3>🛠️ Tools</h3>
                    <button id="autoArrangeBtn" class="btn btn-primary" style="width: 100%;">
                        🎯 Auto Arrange
                    </button>
                    <button id="clearCanvasBtn" class="btn btn-warning" style="width: 100%;">
                        🗑️ Clear Canvas
                    </button>
                    <button id="deleteAllBtn" class="btn btn-danger" style="width: 100%;">
                        💥 Delete All Images
                    </button>
                </div>
            </div>

            <div class="canvas-area">
                <div id="canvas-container">
                    <canvas id="fabricCanvas"></canvas>
                </div>
                <div class="canvas-controls">
                    <button id="zoomInBtn" class="btn btn-primary">🔍 Zoom In</button>
                    <button id="zoomOutBtn" class="btn btn-primary">🔍 Zoom Out</button>
                    <button id="resetZoomBtn" class="btn btn-primary">🎯 Reset Zoom</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DTF Gang Builder - Simple Demo
        class DTFGangBuilder {
            constructor() {
                this.canvas = null;
                this.uploadedImages = [];
                this.currentSheetSize = '30x72';
                this.imageSpacing = 3; // mm
                this.sheetMargins = 5; // mm
                this.selectedImage = null;
                this.defaultDPI = 300;
                this.aspectRatioLocked = true;

                this.init();
            }

            init() {
                this.initCanvas();
                this.initEventListeners();
                this.updateStatus('🔵 Upload an image to get started');
            }

            initCanvas() {
                // Sheet sizes in inches
                const sheetSizes = {
                    '30x12': { width: 30, height: 12 },
                    '30x24': { width: 30, height: 24 },
                    '30x36': { width: 30, height: 36 },
                    '30x48': { width: 30, height: 48 },
                    '30x60': { width: 30, height: 60 },
                    '30x72': { width: 30, height: 72 },
                    '30x100': { width: 30, height: 100 },
                    '30x120': { width: 30, height: 120 }
                };

                const size = sheetSizes[this.currentSheetSize];
                const scale = 8; // pixels per inch for display
                
                this.canvas = new fabric.Canvas('fabricCanvas', {
                    width: size.width * scale,
                    height: size.height * scale,
                    backgroundColor: '#ffffff'
                });

                this.addGrid();
            }

            addGrid() {
                // Add grid lines for visual reference
                const gridSize = 24; // 3 inches at 8px/inch scale
                
                for (let i = 0; i <= this.canvas.width; i += gridSize) {
                    const line = new fabric.Line([i, 0, i, this.canvas.height], {
                        stroke: '#e0e0e0',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.canvas.add(line);
                }

                for (let i = 0; i <= this.canvas.height; i += gridSize) {
                    const line = new fabric.Line([0, i, this.canvas.width, i], {
                        stroke: '#e0e0e0',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.canvas.add(line);
                }
            }

            initEventListeners() {
                // Upload area
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');

                uploadArea.addEventListener('click', () => fileInput.click());
                uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // Controls
                document.getElementById('duplicateBtn').addEventListener('click', this.handleDuplicate.bind(this));
                document.getElementById('fillSheetBtn').addEventListener('click', this.handleFillSheet.bind(this));
                document.getElementById('autoArrangeBtn').addEventListener('click', this.handleAutoArrange.bind(this));
                document.getElementById('clearCanvasBtn').addEventListener('click', this.handleClearCanvas.bind(this));
                document.getElementById('deleteAllBtn').addEventListener('click', this.handleDeleteAll.bind(this));

                // Size controls
                document.getElementById('applySizeBtn').addEventListener('click', this.handleApplySize.bind(this));
                document.getElementById('deleteImageBtn').addEventListener('click', this.handleDeleteImage.bind(this));
                document.getElementById('imageWidth').addEventListener('input', this.handleWidthChange.bind(this));
                document.getElementById('imageHeight').addEventListener('input', this.handleHeightChange.bind(this));
                document.getElementById('maintainAspectRatio').addEventListener('change', this.handleAspectRatioToggle.bind(this));
                document.getElementById('imageDPIControl').addEventListener('change', this.handleDPIChange.bind(this));

                // Sheet settings
                document.getElementById('sheetSize').addEventListener('change', this.handleSheetSizeChange.bind(this));
                document.getElementById('spacing').addEventListener('change', this.handleSpacingChange.bind(this));
                document.getElementById('margins').addEventListener('change', this.handleMarginsChange.bind(this));

                // Zoom controls
                document.getElementById('zoomInBtn').addEventListener('click', () => this.canvas.setZoom(this.canvas.getZoom() * 1.2));
                document.getElementById('zoomOutBtn').addEventListener('click', () => this.canvas.setZoom(this.canvas.getZoom() / 1.2));
                document.getElementById('resetZoomBtn').addEventListener('click', () => this.canvas.setZoom(1));

                // Canvas selection events
                this.canvas.on('selection:created', this.handleSelection.bind(this));
                this.canvas.on('selection:updated', this.handleSelection.bind(this));
                this.canvas.on('selection:cleared', this.handleSelectionCleared.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                e.stopPropagation();
                e.currentTarget.classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                e.currentTarget.classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                e.stopPropagation();
                e.currentTarget.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                console.log('Files dropped:', files);
                this.processFiles(files);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                console.log('Files selected:', files);
                this.processFiles(files);
            }

            processFiles(files) {
                console.log('Processing files:', files.length);
                if (files.length === 0) {
                    this.updateStatus('🔴 No files selected');
                    return;
                }

                files.forEach(file => {
                    console.log('Processing file:', file.name, file.type);
                    if (file.type.startsWith('image/')) {
                        this.updateStatus('🔵 Loading image...');
                        this.loadImage(file);
                    } else {
                        console.log('Skipping non-image file:', file.name);
                    }
                });
            }

            loadImage(file) {
                console.log('Loading image:', file.name);
                const reader = new FileReader();
                reader.onload = (e) => {
                    console.log('FileReader loaded, creating fabric image...');
                    fabric.Image.fromURL(e.target.result, (img) => {
                        console.log('Fabric image created:', img.width, 'x', img.height);
                        // Store original dimensions and metadata
                        img.originalWidth = img.width;
                        img.originalHeight = img.height;
                        img.fileName = file.name;
                        img.fileSize = file.size;
                        img.dpi = this.defaultDPI;

                        // Calculate initial size in inches at 300 DPI
                        const initialWidthInches = img.width / this.defaultDPI;
                        const initialHeightInches = img.height / this.defaultDPI;

                        // Scale image to reasonable display size (max 2 inches at current scale)
                        const maxDisplaySize = 2 * 8; // 2 inches at 8px/inch display scale
                        const scale = Math.min(maxDisplaySize / img.width, maxDisplaySize / img.height);

                        img.scale(scale);
                        img.set({
                            left: 50,
                            top: 50,
                            cornerColor: '#007bff',
                            cornerSize: 8,
                            transparentCorners: false
                        });

                        // Store the actual size in inches
                        img.actualWidthInches = initialWidthInches;
                        img.actualHeightInches = initialHeightInches;

                        this.canvas.add(img);
                        this.canvas.setActiveObject(img);
                        this.canvas.renderAll();

                        this.uploadedImages.push(img);
                        this.selectedImage = img;
                        this.updateImageInfo(img);
                        this.updateStatus('🟢 Image loaded! Adjust size if needed, then duplicate.');
                    });
                };
                reader.readAsDataURL(file);
            }

            handleDuplicate() {
                const quantity = parseInt(document.getElementById('quantitySelect').value);
                const activeObject = this.canvas.getActiveObject();

                if (!activeObject) {
                    const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid);
                    if (imageObjects.length > 0) {
                        this.canvas.setActiveObject(imageObjects[0]);
                        this.updateStatus('🔵 Selected first image. Click "Create Selected Quantity" again to duplicate.');
                        return;
                    } else {
                        this.updateStatus('🔴 Please upload an image first');
                        return;
                    }
                }

                this.massDuplicate(activeObject, quantity);
            }

            massDuplicate(sourceObject, count) {
                const spacingPixels = this.mmToPixels(this.imageSpacing);
                const marginPixels = this.mmToPixels(this.sheetMargins);

                const objWidth = sourceObject.getScaledWidth();
                const objHeight = sourceObject.getScaledHeight();

                const canvasWidth = this.canvas.width - (marginPixels * 2);
                const itemsPerRow = Math.floor((canvasWidth + spacingPixels) / (objWidth + spacingPixels));

                let created = 0;
                const positions = [];

                // Calculate all positions first
                for (let i = 0; i < count; i++) {
                    const row = Math.floor(i / itemsPerRow);
                    const col = i % itemsPerRow;

                    const x = marginPixels + (col * (objWidth + spacingPixels));
                    const y = marginPixels + (row * (objHeight + spacingPixels));

                    if (y + objHeight <= this.canvas.height - marginPixels) {
                        positions.push({ x, y });
                    }
                }

                // Create clones with preserved metadata
                positions.forEach((pos, index) => {
                    sourceObject.clone((cloned) => {
                        // Preserve image metadata
                        if (sourceObject.fileName) {
                            cloned.originalWidth = sourceObject.originalWidth;
                            cloned.originalHeight = sourceObject.originalHeight;
                            cloned.fileName = sourceObject.fileName;
                            cloned.fileSize = sourceObject.fileSize;
                            cloned.dpi = sourceObject.dpi;
                            cloned.actualWidthInches = sourceObject.actualWidthInches;
                            cloned.actualHeightInches = sourceObject.actualHeightInches;
                        }

                        cloned.set({
                            left: pos.x,
                            top: pos.y
                        });
                        this.canvas.add(cloned);
                        created++;

                        if (created === positions.length) {
                            this.canvas.renderAll();
                            const sizeInfo = sourceObject.actualWidthInches ?
                                ` (${sourceObject.actualWidthInches.toFixed(2)}" × ${sourceObject.actualHeightInches.toFixed(2)}" at ${sourceObject.dpi} DPI)` : '';
                            this.updateStatus(`🟢 Created ${created} copies with professional spacing!${sizeInfo}`);
                        }
                    });
                });
            }

            handleFillSheet() {
                const activeObject = this.canvas.getActiveObject();
                
                if (!activeObject) {
                    const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid);
                    if (imageObjects.length > 0) {
                        this.canvas.setActiveObject(imageObjects[0]);
                        this.updateStatus('🔵 Selected first image. Click "Fill Entire Sheet" again to continue.');
                        return;
                    } else {
                        this.updateStatus('🔴 Please upload an image first');
                        return;
                    }
                }

                // Calculate how many fit
                const spacingPixels = this.mmToPixels(this.imageSpacing);
                const marginPixels = this.mmToPixels(this.sheetMargins);
                
                const objWidth = activeObject.getScaledWidth();
                const objHeight = activeObject.getScaledHeight();
                
                const canvasWidth = this.canvas.width - (marginPixels * 2);
                const canvasHeight = this.canvas.height - (marginPixels * 2);
                
                const itemsPerRow = Math.floor((canvasWidth + spacingPixels) / (objWidth + spacingPixels));
                const rowsPerSheet = Math.floor((canvasHeight + spacingPixels) / (objHeight + spacingPixels));
                
                const totalItems = itemsPerRow * rowsPerSheet;

                if (totalItems <= 1) {
                    this.updateStatus('🔴 Object is too large to duplicate on this sheet size');
                    return;
                }

                if (confirm(`This will create ${totalItems} copies to fill the entire sheet. Continue?`)) {
                    this.massDuplicate(activeObject, totalItems - 1);
                }
            }

            handleAutoArrange() {
                const imageObjects = this.canvas.getObjects().filter(obj => !obj.isGrid);
                
                if (imageObjects.length === 0) {
                    this.updateStatus('🔴 No images to arrange');
                    return;
                }

                const spacingPixels = this.mmToPixels(this.imageSpacing);
                const marginPixels = this.mmToPixels(this.sheetMargins);

                let currentX = marginPixels;
                let currentY = marginPixels;
                let rowHeight = 0;
                let arranged = 0;

                imageObjects.forEach(obj => {
                    const objWidth = obj.getScaledWidth();
                    const objHeight = obj.getScaledHeight();

                    if (currentX + objWidth > this.canvas.width - marginPixels) {
                        currentX = marginPixels;
                        currentY += rowHeight + spacingPixels;
                        rowHeight = 0;
                    }

                    if (currentY + objHeight <= this.canvas.height - marginPixels) {
                        obj.set({ left: currentX, top: currentY });
                        currentX += objWidth + spacingPixels;
                        rowHeight = Math.max(rowHeight, objHeight);
                        arranged++;
                    }
                });

                this.canvas.renderAll();
                const efficiency = Math.round((arranged / imageObjects.length) * 100);
                this.updateStatus(`🟢 Auto-arranged ${arranged} images with ${efficiency}% efficiency!`);
            }

            handleClearCanvas() {
                if (confirm('Clear all images from the canvas?')) {
                    const objects = this.canvas.getObjects();
                    objects.forEach(obj => {
                        if (!obj.isGrid) {
                            this.canvas.remove(obj);
                        }
                    });
                    this.canvas.renderAll();
                    this.uploadedImages = [];
                    this.selectedImage = null;

                    // Hide info panels
                    document.getElementById('imageInfoSection').style.display = 'none';
                    document.getElementById('sizeControlsSection').style.display = 'none';

                    this.updateStatus('🔵 Canvas cleared. Upload an image to get started.');
                }
            }

            handleDeleteAll() {
                const imageCount = this.uploadedImages.length;
                if (imageCount === 0) {
                    this.updateStatus('🔴 No images to delete');
                    return;
                }

                if (confirm(`Delete all ${imageCount} images from the canvas?`)) {
                    // Remove all non-grid objects
                    const objects = this.canvas.getObjects();
                    objects.forEach(obj => {
                        if (!obj.isGrid) {
                            this.canvas.remove(obj);
                        }
                    });

                    // Clear arrays and selection
                    this.uploadedImages = [];
                    this.selectedImage = null;
                    this.canvas.discardActiveObject();
                    this.canvas.renderAll();

                    // Hide info panels
                    document.getElementById('imageInfoSection').style.display = 'none';
                    document.getElementById('sizeControlsSection').style.display = 'none';

                    this.updateStatus(`🟢 Deleted all ${imageCount} images successfully`);
                }
            }

            handleSheetSizeChange(e) {
                this.currentSheetSize = e.target.value;
                this.canvas.clear();
                this.initCanvas();
                this.uploadedImages = [];
                this.updateStatus('🔵 Sheet size changed. Upload images to continue.');
            }

            handleSpacingChange(e) {
                this.imageSpacing = parseFloat(e.target.value);
            }

            handleMarginsChange(e) {
                this.sheetMargins = parseFloat(e.target.value);
            }

            mmToPixels(mm) {
                // Convert mm to pixels (assuming 300 DPI and 8px/inch display scale)
                return (mm / 25.4) * 8;
            }

            updateStatus(message) {
                const statusBox = document.getElementById('statusBox');
                statusBox.textContent = message;

                if (message.includes('🔴')) {
                    statusBox.className = 'status-box status-error';
                } else if (message.includes('🟢')) {
                    statusBox.className = 'status-box status-success';
                } else {
                    statusBox.className = 'status-box status-info';
                }
            }

            // New methods for image size and DPI handling
            updateImageInfo(img) {
                console.log('updateImageInfo called with:', img);
                if (!img) {
                    console.log('No image provided to updateImageInfo');
                    return;
                }

                console.log('Image properties:', {
                    fileName: img.fileName,
                    originalWidth: img.originalWidth,
                    originalHeight: img.originalHeight,
                    actualWidthInches: img.actualWidthInches,
                    actualHeightInches: img.actualHeightInches,
                    dpi: img.dpi
                });

                // Update image info display
                document.getElementById('imageFileName').textContent = img.fileName || 'Unknown';
                document.getElementById('imageOriginalSize').textContent =
                    `${img.originalWidth || 0} × ${img.originalHeight || 0} px`;
                document.getElementById('imageCurrentSize').textContent =
                    `${(img.actualWidthInches || 0).toFixed(2)}" × ${(img.actualHeightInches || 0).toFixed(2)}"`;
                document.getElementById('imageDPI').textContent = img.dpi || this.defaultDPI;

                // Update size controls
                document.getElementById('imageWidth').value = (img.actualWidthInches || 0).toFixed(2);
                document.getElementById('imageHeight').value = (img.actualHeightInches || 0).toFixed(2);
                document.getElementById('imageDPIControl').value = img.dpi || this.defaultDPI;

                // Show the info sections
                console.log('Showing image info sections');
                document.getElementById('imageInfoSection').style.display = 'block';
                document.getElementById('sizeControlsSection').style.display = 'block';
            }

            handleSelection(e) {
                console.log('handleSelection called with:', e);
                const activeObject = e.target;
                console.log('Active object:', activeObject);
                console.log('Is grid?', activeObject?.isGrid);
                console.log('Has fileName?', activeObject?.fileName);

                if (activeObject && !activeObject.isGrid && activeObject.fileName) {
                    console.log('Setting selected image and updating info');
                    this.selectedImage = activeObject;
                    this.updateImageInfo(activeObject);
                } else {
                    console.log('Object does not meet criteria for selection');
                }
            }

            handleSelectionCleared() {
                console.log('Selection cleared');
                this.selectedImage = null;
                document.getElementById('imageInfoSection').style.display = 'none';
                document.getElementById('sizeControlsSection').style.display = 'none';
            }

            handleWidthChange(e) {
                if (!this.selectedImage) return;

                const newWidth = parseFloat(e.target.value);
                if (isNaN(newWidth) || newWidth <= 0) return;

                if (this.aspectRatioLocked) {
                    const aspectRatio = this.selectedImage.actualWidthInches / this.selectedImage.actualHeightInches;
                    const newHeight = newWidth / aspectRatio;
                    document.getElementById('imageHeight').value = newHeight.toFixed(2);
                }
            }

            handleHeightChange(e) {
                if (!this.selectedImage) return;

                const newHeight = parseFloat(e.target.value);
                if (isNaN(newHeight) || newHeight <= 0) return;

                if (this.aspectRatioLocked) {
                    const aspectRatio = this.selectedImage.actualWidthInches / this.selectedImage.actualHeightInches;
                    const newWidth = newHeight * aspectRatio;
                    document.getElementById('imageWidth').value = newWidth.toFixed(2);
                }
            }

            handleAspectRatioToggle(e) {
                this.aspectRatioLocked = e.target.checked;
            }

            handleDPIChange(e) {
                const newDPI = parseInt(e.target.value);
                if (this.selectedImage) {
                    this.selectedImage.dpi = newDPI;
                    this.updateImageInfo(this.selectedImage);
                }
            }

            handleApplySize() {
                if (!this.selectedImage) {
                    this.updateStatus('🔴 Please select an image first');
                    return;
                }

                const newWidthInches = parseFloat(document.getElementById('imageWidth').value);
                const newHeightInches = parseFloat(document.getElementById('imageHeight').value);
                const newDPI = parseInt(document.getElementById('imageDPIControl').value);

                if (isNaN(newWidthInches) || isNaN(newHeightInches) || newWidthInches <= 0 || newHeightInches <= 0) {
                    this.updateStatus('🔴 Please enter valid width and height values');
                    return;
                }

                // Update the image properties
                this.selectedImage.actualWidthInches = newWidthInches;
                this.selectedImage.actualHeightInches = newHeightInches;
                this.selectedImage.dpi = newDPI;

                // Calculate new display scale (8 pixels per inch for display)
                const displayScale = 8;
                const newDisplayWidth = newWidthInches * displayScale;
                const newDisplayHeight = newHeightInches * displayScale;

                // Calculate scale factors based on original image dimensions
                const scaleX = newDisplayWidth / this.selectedImage.originalWidth;
                const scaleY = newDisplayHeight / this.selectedImage.originalHeight;

                // Apply the new scale
                this.selectedImage.set({
                    scaleX: scaleX,
                    scaleY: scaleY
                });

                this.canvas.renderAll();
                this.updateImageInfo(this.selectedImage);
                this.updateStatus(`🟢 Image resized to ${newWidthInches}" × ${newHeightInches}" at ${newDPI} DPI`);
            }

            handleDeleteImage() {
                if (!this.selectedImage) {
                    this.updateStatus('🔴 Please select an image to delete');
                    return;
                }

                if (confirm(`Delete "${this.selectedImage.fileName || 'selected image'}"?`)) {
                    // Remove from canvas
                    this.canvas.remove(this.selectedImage);

                    // Remove from uploaded images array
                    const index = this.uploadedImages.indexOf(this.selectedImage);
                    if (index > -1) {
                        this.uploadedImages.splice(index, 1);
                    }

                    // Clear selection
                    this.selectedImage = null;
                    this.canvas.discardActiveObject();
                    this.canvas.renderAll();

                    // Hide info panels
                    document.getElementById('imageInfoSection').style.display = 'none';
                    document.getElementById('sizeControlsSection').style.display = 'none';

                    this.updateStatus('🟢 Image deleted successfully');
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DTFGangBuilder();
        });
    </script>
</body>
</html>
